export const config = {
	default: 'mp',
	sd: {
		host: 'https://sdhr.bigbangmarkerdev.info',
		lang: 'hr',
		apiCache: false,
		routeCacheExpire: 0, // 5 min
		redis: {
			driver: 'redis',
			host: 'redis_sdhr_bigbangmarkerdev_info_nuxt',
			port: 6379,
			username: 'default',
			password: 'LqBKEDGSBM',
			base: 'sdhr',
			ttl: 0, // 1 hour
		},
	},
	mp: {
		host: 'https://mp.bigbangmarkerdev.info',
		lang: 'si',
		apiCache: false,
		routeCacheExpire: 0, // 5 min
		componentCache: false,
		redis: {
			driver: 'redis',
			host: '***********',
			port: 6380,
			username: 'default',
			password: '<PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON>',
			base: 'mp_bigbang',
			ttl: 0, // 1 hour
		},
	},
	beta: {
		host: 'https://beta.bigbang.si',
		lang: 'si',
		apiCache: false,
		routeCacheExpire: 0, // 5 min
		componentCache: false,
		redis: {
			driver: 'redis',
			host: 'redis-bigbang<PERSON><PERSON>',
			port: 6379,
			username: 'default',
			password: '<PERSON>qBKEDGS<PERSON>',
			base: 'beta_bigbang',
			ttl: 0,
		},
	},
};