{"private": true, "scripts": {"dev": "nuxt dev", "build": "node ..\\nuxt-base\\buildversion.js && nuxt build", "build-mac": "node ../nuxt-base/buildversion.js; nuxt build", "builddev": "nuxt build --mode=development", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "^2.4.0", "@vueuse/core": "13.1.0", "nuxt": "3.17.3", "nuxt-delay-hydration": "^1.3.8"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@panzoom/panzoom": "^4.6.0", "@vueform/slider": "^2.1.10", "less": "4.3.0", "less-loader": "12.3.0", "photoswipe": "^5.4.4", "swiper": "^11.2.6", "vee-validate": "4.15.0"}}