export default defineNuxtRouteMiddleware(async to => {
	// match url that starts with /p/ and has a number after it
	const regex = to.path.match(/^\/p\/(\d+)(\/|$)/);

	if (regex) {
		const catalog = useCatalog();
		const {getUrlSegments} = useUrl();
		const code = getUrlSegments(to.path)[1];
		if (!code) return;

		try {
			const {data} = await catalog.fetchProducts({
				code: code,
				mode: 'url',
				limit: 1,
			});
			if (!data.value.data?.items?.length) return;
			return navigateTo(data.value.data.items[0].url_without_domain);
		} catch (e) {
			console.log(e);
		}
	}
});