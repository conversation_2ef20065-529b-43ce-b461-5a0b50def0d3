<template>
	<BaseSearchForm :fetch="searchFormConfig" :reset-on-empty-input="false" v-slot="{searchResults, updateValue, totalSearchResults, searchTerm, loading, handleInput, resetInput, onReset, selectedIndex, onSubmit}" @afterEscape="onEscape" ref="searchForm">
		<Body :class="{'o-active': totalSearchResults}" />
		<div class="sw" :class="{'loading': loading, 'active': formActive}">
			<div @click="toggleSearchForm()" class="sw-toggle" :class="{'special': searchTerm, 'active': formActive}"><BaseCmsLabel code='close' /></div>
			<div class="sw-form" id="main_search">
				<input class="sw-input" ref="swInput" name="search_q" id="search_q" type="text" autocomplete="off" :value="searchTerm" @focus="formActive = true" @input="updateValue" @keyup="handleInput" />
				<label class="sw-label" :class="{'hidden': searchTerm}" for="search_q" v-html="labels.get('enter_search_term')" />

				<div v-if="loading" class="sw-loading"></div>
				<div v-if="searchTerm" @click="resetInput(), swInput.focus()" class="sw-clear"></div> <!-- X za poništavanje inputa -->
				<button @click="onSubmit()" :disabled="!searchTerm" class="sw-submit" :class="{'active': searchTerm}" type="submit">{{ labels.get('search_button') }}</button>
			</div>

			<ClientOnly>
				<div class="ac" v-if="totalSearchResults">
					<div class="ac-wrapper wrapper">
						<div class="ac-col ac-col1">
							<!-- FIXME pojmovi -->
							<div class="ac-col-title">Iz vaše pretrage</div>
							<NuxtLink @click="onReset()" to="#" class="ac-term-item">Lorem</NuxtLink>
							<NuxtLink @click="onReset()" to="#" class="ac-term-item">Ipsum</NuxtLink>
							<NuxtLink @click="onReset()" to="#" class="ac-term-item">Dolor sit</NuxtLink>
						</div>
						<div class="ac-col ac-col2" v-if="searchResults.catalogproduct">
							<div class="ac-col-title special">
								<BaseCmsLabel code='autocomplete_products' tag="span" />
								<div v-if="searchResults.catalogproduct_show_all.total" class="ac-showall-btn" @click="onSubmit"><BaseCmsLabel code='show_all' tag="span" /></div>
							</div>
							<NuxtLink @click="onReset()" v-for="item in searchResults.catalogproduct" :key="item.id" :to="item.url_without_domain" class="ac-item">
								<div class="ac-item-img" :class="{'xxx': item?.xxx}">
									<span>
										<BaseUiImage loading="lazy" :src="item.image_upload_path" width="90" height="90" />
									</span>
									<div v-if="item?.xxx" class="ac-item-img-xxx"></div>
								</div>
								<div class="ac-item-cnt">
									<div class="ac-item-title" :class="{'xxx': item?.xxx}">{{ item.title }}</div>
									<div class="ac-item-price">
										<span class="ac-item-current-price" :class="{'discount': item.basic_price}">{{ item.price }}</span>
										<span v-if="item.basic_price" class="ac-item-basic-price">{{ item.basic_price }}</span>
										<span v-if="item.basic_price" class="ac-item-price-info">
											<span class="ac-item-price-tooltip"><BaseCmsLabel code='lowest_price' /></span>
										</span>
									</div>
									<div v-if="item?.xxx" @click.prevent="verificationXXX" class="ac-item-xxx-btn verification_xxx_btn"><BaseCmsLabel code="xxx_img_btn" /></div>
								</div>
							</NuxtLink>
						</div>
						<div v-if="searchResults.catalogcategory" class="ac-col ac-col3">
							<div class="ac-col-title"><BaseCmsLabel code='autocomplete_categories' /></div>
							<NuxtLink @click="onReset()" v-for="item in searchResults.catalogcategory" :key="item.id" :to="item.url_without_domain" class="ac-category-item">
								<span v-if="item.image_upload_path" class="img"><BaseUiImage loading="lazy" :src="item.image_upload_path" width="90" height="90" /></span>
								<span class="title">{{ item.title }}</span>
							</NuxtLink>
						</div>
					</div>
				</div>
			</ClientOnly>
		</div>
	</BaseSearchForm>
</template>

<script setup>
	const labels = useLabels();
	const {onClickOutside} = useDom();
	const {emit} = useEventBus();
	const {mobileBreakpoint} = inject('rwd');
	const {fixedHeader} = inject('layout');

	const searchFormConfig = {
		'allow_models': ['catalogcategory', 'catalogproduct'],
		'result_fields': {
			'catalogproduct': ['image','price','basic_price','discount_percent','xxx'],
		},
		'result_per_page': {
			'catalogproduct': 3,
			'_default': 5
		},
		'result_image': '90x90_r'
	}

	// close form on outside click and escape keyup
	const searchForm = ref(null);
	const swInput = ref(null);
	const formActive = ref(false);

	function onEscape() {
		searchForm.value.onReset();
	}

	let timeout;
	function toggleSearchForm() {
		if(timeout) clearTimeout(timeout);
		formActive.value = !formActive.value;
		if(fixedHeader.value) {
			timeout = setTimeout(() => {
				swInput.value.focus();
			},200);
		}
		searchForm.value.onReset();
	}

	//XXX verification
	function verificationXXX() {
		emit('xxx');
	}
</script>

<style lang="less" scoped>
	.sw{
		z-index: 11;
		&.active{
			&:before{.pseudo(auto, 88px); background: var(--blue); left: 164px; right: -20px; top: -22px; bottom: 0;}
			.sw-form{width: 900px;}
		}

		@media (max-width: @l){
			&.active .sw-form{width: 780px;}
		}
		@media (max-width: @t){
			&.active{
				&:before{height: 56px; left: 105px; right: -10px; top: -9px;}
				.sw-form{width: 650px;}
			}
		}
		@media (max-width: @m){
			width: 100%; margin-top: 18px; order: 4; position: relative;
			&.active{
				&:before{content: none;}
				.sw-form{width: 100%;}
			}
		}
	}
	.sw-form{
		display: flex; width: 708px; position: relative; .transition(width);

		@media (max-width: @t){width: 550px;}
		@media (max-width: @m){width: 100%;}
	}
	.sw-input{
		width: 100%; height: 44px; padding: 10px 110px 10px 18px; border: none; border-radius: 22px;

		@media (max-width: @t){height: 38px;}
	}
	.sw-label{
		padding: 0; font-size: 15px; color: var(--gray5); position: absolute; left: 18px; top: 12px; cursor: text;z-index: 1;
		&.hidden{display: none;}

		@media (max-width: @t){font-size: 14px; top: 9px;}
}
	.sw-submit{
		display: flex; align-items: center; justify-content: center; width: 54px; height: 44px; padding: 0; background: var(--white); font-size: 0; line-height: 0; border-radius: 0 22px 22px 0; position: absolute; right: 0; top: 0;
		&:before{.icon-search(); font: 20px/1 var(--fonti); color: var(--gray5);}
		&.active{
			cursor: pointer;
			&:before{color: var(--black);}
		}

		@media (max-width: @t){
			width: 50px; height: 38px; border-radius: 0 19px 19px 0;
			&:before{font-size: 19px; position: absolute; right: 18px;}
		}
	}
	.sw-clear{
		display: flex; align-items: center; justify-content: center; width: 52px; height: 44px; background: var(--white); font-size: 0; line-height: 0; position: absolute; right: 53px; top: 0; cursor: pointer;
		&:before{.icon-x(); font: 20px/1 var(--fonti); color: var(--black);}
		&:after{.pseudo(1px,31px); background: var(--placeholderColor); position: absolute; right: 0; z-index: 1;}

		@media (max-width: @t){
			height: 38px; right: 51px;
			&:after{height: 21px;}
		}
		@media (max-width: @m){display: none;}
	}
	.sw-loading{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 44px; height: 44px; background: #fff url(assets/images/loader.svg) no-repeat center; background-size: 33px; position: absolute; right: 105px; z-index: 1;

		@media (max-width: @t){width: 38px; height: 38px; background-size: 30px; right: 100px;}
		@media (max-width: @m){right: 90px;}
	}
	.sw-toggle{
		display: none; align-items: center; justify-content: center; font-size: 16px; color: #fff; position: absolute; top: 13px; right: 0; cursor: pointer;
		&.active{display: flex;}

		@media (max-width: @t){top: 9px;}
		@media (max-width: @m){
			display: none; width: 48px; height: 38px; background: var(--white); font-size: 0; line-height: 0; position: absolute; right: 50px; top: 0; z-index: 11;
			&:before{.icon-x(); font: 19px/1 var(--fonti); color: var(--black);}
			&:after{.pseudo(1px,21px); background: var(--placeholderColor); position: absolute; right: 0; z-index: 1;}
			&.active{display: none;}
			&.special{display: flex;}
		}
	}

	//autocomplete
	.ac{
		height: calc(~"100vh - 88px"); overflow-x: hidden; overflow-y: auto; position: fixed; top: 88px; left: 0; right: 0; bottom: 0; background: var(--white); z-index: 111;
		
		@media (max-width: @t){height: calc(~"100vh - 56px"); top: 56px;}
		@media (max-width: @m){height: calc(~"100vh - 104px"); top: 104px;}
	}
	.ac-wrapper{
		display: grid; grid-template-columns: repeat(3, 1fr); grid-column-gap: var(--elementGap); padding: 48px 0; background: var(--white);
		
		@media (max-width: @t){grid-column-gap: 16px; padding: 32px 0;}
		@media (max-width: @m){display: block; grid-column-gap: 0; padding: 12px 0;}
	}
	.ac-col{
		position: relative;

		@media (max-width: @m){margin-bottom: 24px;}
	}
	.ac-col1 .ac-col-title{
		@media (max-width: @ms){display: none;}
	}
	.ac-col-title{
		display: flex; align-items: center; margin-bottom: 24px; padding-bottom: 14px; border-bottom: 1px solid var(--placeholderColor); font-size: 14px; font-weight: 600;
		&.special{margin-bottom: 0;}
		span{flex-grow: 1;}
	}
	.ac-showall-btn{
		display: none;

		@media (max-width: @m){
			display: block;
			span{
				display: flex; align-items: center; padding-right: 24px; position: relative;
				&:before{.icon-arrow-right(); font: 13px/1 var(--fonti); color: #000; position: absolute; right: 0;}
			}
		}
	}
	.ac-term-item{
		display: flex; align-items: center; padding: 12px 8px; font-size: 14px; font-weight: 300; color: var(--black); text-decoration: none; position: relative;
		&:before{.icon-arrow-left(); font: 18px/1 var(--fonti); color: var(--gray5); position: absolute; right: 8px;}

		@media (max-width: @m){
			padding: 8px;
			&:before{font-size: 14px;}
		}
	}

	.ac-item{
		display: flex; align-items: flex-start; padding: 32px 8px 16px; background: var(--white); border-bottom: 1px solid var(--placeholderColor); color: var(--black); text-decoration: none;
		
		@media (max-width: @m){
			padding: 24px 8px 16px;
			&:last-child{border-bottom: 0; padding-bottom: 8px;}
		}
	}
	.ac-item-img{
		display: flex; justify-content: center; align-items: center; width: 90px; height: 90px; flex-shrink: 0; margin-right: 16px; position: relative;
		&.xxx span{mix-blend-mode: multiply; filter: blur(3px);}
		:deep(span){display: flex; justify-content: center; align-items: center; width: 90px; height: 90px;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%;}
	}
	.ac-item-img-xxx{
		display: flex; align-items: center; justify-content: center; position: absolute; top: 0; right: 0; left: 0; bottom: 0; z-index: 1;
		&:before{.pseudo(52px,52px); .icon-age(); font: 52px/1 var(--fonti); color: var(--black); z-index: 1;}
	}
	.ac-item-cnt{display: block; width: 100%; position: relative;}
	.ac-item-title{
		display: block; font-size: 12px; position: relative;
		&.xxx{mix-blend-mode: multiply; filter: blur(3px);}
	}
	.ac-item-price{display: flex; align-items: flex-end; margin-top: 10px; font-size: 16px; font-weight: 700; position: relative;}
	.ac-item-current-price.discount{color: var(--red);}
	.ac-item-basic-price{margin-left: 8px; font-size: 12px; font-weight: 400; letter-spacing: -0.24px; color: var(--black); text-decoration: line-through;}
	.ac-item-price-info{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 14px; height: 14px; margin-bottom: 2px; margin-left: 6px; position: relative;
		&:before{.icon-info(); font: 14px/1 var(--fonti); color: var(--black);}
		&:hover .ac-item-price-tooltip{display: flex;}
	}
	.ac-item-price-tooltip{
		display: none; align-items: center; white-space: nowrap; padding: 6px 8px; border-radius: 6px; background: var(--gray3); font-size: 11px; font-weight: normal; color: #000; position: absolute; left: -10px; bottom: calc(~"100% - -8px");
		&:before{.pseudo(8px,8px); background: var(--gray3); position: absolute; top: calc(~"100% - 5px"); left: 12px; z-index: 1; .rotate(45deg);}
	}
	.ac-item-xxx-btn{
		font-size: 12px; text-decoration: underline; color: var(--black); position: absolute; bottom: 0; right: 0;
		@media (min-width: @t){
			&:hover{text-decoration: none;}
		}
	}
	
	.ac-category-item{
		display: flex; align-items: center; margin-bottom: 16px; font-size: 12px; color: #000; text-decoration: none;
		.img{display: flex; align-items: center; justify-content: center; width: 48px; height: 48px; margin-right: 16px; background: var(--gray3); border-radius: 8px;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%;}

		@media (max-width: @m){margin-bottom: 12px;}
	}

	//fixed header
	.fixed-header{
		.sw{
			position: absolute; right: 163px;
			&.active{
				position: initial; right: unset;
				&:before{height: 65px; left: 129px; right: -20px; top: -11px; bottom: 0;}
				.sw-form{display: flex;}
				.sw-toggle{
					width: auto; height: auto; font-size: 16px; line-height: 1.4; position: absolute; top: 12px; right: 0;
					&:before{display: none;}
				}
			}
		}
		.sw-form{display: none;}
		.sw-toggle{
			display: flex; width: 30px; height: 30px; font-size: 0; line-height: 0; position: relative; top: unset; right: unset;
			&:before{.icon-search(); font: 22px/1 var(--fonti); color: var(--white);}
		}

		.ac{height: calc(~"100vh - 65px"); top: 65px};

		@media (max-width: @t){
			.sw{
				right: 142px;
				&.active{
					&:before{height: 55px; left: 110px; top: -11px; bottom: 0;}
					.sw-toggle{top: 8px;}
				}
			}
			.sw-toggle{
				width: 30px; height: 30px;
				&:before{font-size: 19px;}
			}

			.ac{height: calc(~"100vh - 55px"); top: 55px};
		}
		@media (max-width: @m){
			.sw{
				width: auto; right: 123px; margin-top: 0;
				&.active{
					width: 100%; margin-top: 18px; right: unset; position: relative;
					&:before{height: 55px; left: 110px; top: -11px; bottom: 0;}
					.sw-toggle{
						display: none; width: 48px; height: 38px; background: var(--white); font-size: 0; line-height: 0; right: 50px; top: 0; z-index: 11;
						&:before{display: block; .icon-x(); font: 19px/1 var(--fonti); color: var(--black);}
						&:after{.pseudo(1px,21px); background: var(--placeholderColor); position: absolute; right: 0; z-index: 1;}
						&.active{display: flex;}
						&.special{display: flex;}
					}
				}
			}
			.sw-toggle{
				background: transparent;
				&:after{background: transparent;}
			}

			.ac{height: calc(~"100vh - 110px"); top: 110px};
		}
	}
</style>