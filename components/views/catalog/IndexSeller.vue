<template>
	<BaseCmsPage v-slot="{page}">
		<div class="wrapper">
			<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
		</div>

		<ClientOnly>
			<div class="special-sellers">
				<div class="wrapper">
					<h2 class="special-sellers-title"><BaseCmsLabel code="special_sellers" /></h2>
				</div>
				<CatalogSellers :fetch="{'status': ['Open', 'Closed'], 'sort': 'grade', limit: 5, special: 1}" :sort="false" thumb-preset="seller" v-slot="{items}">
					<UiSwiper
						name="sellers"
						:options="{
							slidesPerView: 1.1,
							slidesPerGroup: 1,
							spaceBetween: 13,
							breakpoints: {
								1300: {
									slidesPerView: 3,
									slidesPerGroup: 3,
								},
								850: {
									slidesPerView: 2.1,
									slidesPerGroup: 2,
								}
							}
						}" v-if="items?.length">
						<BaseUiSwiperSlide v-for="item in items" :key="item.id">
							<CatalogIndexEntrySeller :item="item" />
						</BaseUiSwiperSlide>
					</UiSwiper>
				</CatalogSellers>
			</div>
		</ClientOnly>
		
		<div class="wrapper">
			<ClientOnly>
				<div class="header">
					<h2><BaseCmsLabel code="all_sellers" /></h2>
					<BaseCatalogSort v-slot="{onSort, items, selected}" :sort-options="['', 'grade_weak', 'az', 'za']" mode="sellers">
						<FormSelect :label="labels.get('sort')" class="sort sort-menu-right">
							<BaseFormSelectOption v-for="item in items" :key="item" :value="item" :selected="item == selected" @select="onSort(item)">
								<BaseCmsLabel v-if="item == ''" code="ordering_top_rated" />
								<BaseCmsLabel v-else-if="item == 'grade_weak'" code="ordering_weak_rated" />
								<BaseCmsLabel v-else :code="'ordering_'+item" />
							</BaseFormSelectOption>
						</FormSelect>
					</BaseCatalogSort>
				</div>
			</ClientOnly>

			<ClientOnly>
				<CatalogSellers :fetch="{'status': ['Open', 'Closed'], 'sort': 'grade'}" v-slot="{items, onSearch, alphabet, onFilter, activeLetter}" thumb-preset="seller">
					<div class="search">
						<input type="text" :placeholder="labels.get('search_sellers')" @input="onSearch" />
					</div>

					<div class="alphabet">
						<div class="alphabet-item" :class="{'active': activeLetter == item}" v-for="item in alphabet" :key="item" @click="onFilter(item)">{{ item }}</div>
					</div>

					<div class="sellers" v-if="items?.length">
						<CatalogIndexEntrySeller v-for="item in items" :key="item.id" :item="item" />
					</div>
					<BaseCmsLabel v-else code="no_sellers" tag="div" class="empty" />
				</CatalogSellers>
				<template #fallback>
					<UiLoader class="spacing" />
				</template>
			</ClientOnly>
		</div>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
</script>

<style lang="less" scoped>
	*{
		--sellersMargin: 30px;
		@media (max-width: @m){
			--sellersMargin: 25px;
		}
	}
	.loader{padding: 100px 0;}
	.sellers{display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 13px; padding-bottom: clamp(30px, 4vw, 60px);}
	.header{display: flex; justify-content: space-between; align-items: center; padding-bottom: var(--sellersMargin);}
	h2{font-size: clamp(24px, 3vw, 28px); font-weight: bold; padding: 0;}

	.search{
		height: 40px; position: relative; margin-bottom: var(--sellersMargin);
		input{
			border-radius: 100px; background: #fff; border: none; height: 100%; padding: 0 20px; font-size: 15px;
			@media (max-width: @m){font-size: 14px;}
		}
		&:after{.icon-search(); font: 20px/1 var(--fonti); position: absolute; top: 0; right: 20px; color: var(--gray5); display: flex; align-items: center; justify-content: center; height: 100%;}
	}

	.alphabet{
		display: flex; gap: 8px; margin-bottom: var(--sellersMargin); flex-wrap: wrap;
		@media (max-width: @m){
			flex-wrap: nowrap; overflow: auto; margin-inline: calc(var(--wrapperMargin) * -1); padding: 0 var(--wrapperMargin); 
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
		}
	}
	.alphabet-item{
		display: flex; width: 40px; height: 40px; flex: 0 0 40px; align-items: center; justify-content: center; border-radius: 100px; background: #fff; border: 1px solid var(--gray2); color: var(--gray5); font-size: 15px; line-height: 1; cursor: pointer; transition: color 0.3s ease, border-color 0.3s ease;
		@media (min-width: @m){
			&:hover{color: var(--blueDark); border-color: var(--blueDark);}
		}
		&.active{color: var(--blueDark); border-color: var(--blueDark);}
	}

	.special-sellers{margin-bottom: var(--sellersMargin);}
	.special-sellers-title{padding-bottom: 20px;}
	:deep(.swiper-slide){height: auto;}
	:deep(.swiper){overflow: initial;}
	.empty{padding: 0 0 40px;}

	@media (max-width: @m){
		.bc{margin-bottom: 20px;}
	}
</style>
