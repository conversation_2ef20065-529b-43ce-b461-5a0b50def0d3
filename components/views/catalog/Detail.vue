<template>
	<BaseCatalogDetail log v-slot="{item}">
		<CmsBreadcrumbs v-if="item?.breadcrumbs" :items="item.breadcrumbs" class="cd-bc wrapper" :remove-last="true" :links="true" />
		<div class="cd-main wrapper">
			<div class="cd-col1">
				<!-- images -->
				<CatalogProductImages :item="item" />

				<!-- attributes -->
				<BaseUiAccordion v-if="displayedAttributes?.length || item?.content || item?.element_content_state">
					<BaseUiAccordionPanel id="state" active v-if="item?.element_content_state">
						<!-- product state -->
						<template #header><BaseCmsLabel code='tab_product_state_description' /></template>
						<template #body>
							<div class="cd-tab-content cd-tab-content-desc" v-interpolation v-html="item.element_content_state" />
						</template>
					</BaseUiAccordionPanel>
					<BaseUiAccordionPanel id="specs" active v-if="displayedAttributes?.length">
						<template #header><BaseCmsLabel code="tab_specs" /></template>
						<template #body>
							<div class="cd-tab-content cd-tab-attributes">
								<div v-for="(item, key) in displayedAttributes" :key="key" class="cd-tab-attribute">
									<div class="cd-tab-attribute-title">{{ item.title }}</div>
									<div class="cd-tab-attribute-desc">
										{{ item.values.join(', ') }}
										<template v-if="item.unit"> {{ item.unit }}</template>
									</div>
								</div>
							</div>
							<div v-if="Object.keys(itemsAttributes)?.length > 6" class="cd-tab-more cd-tab-more-attr" @click="showAttr = !showAttr">{{ showAttr ? labels.get('show_less') : labels.get('show_more') }}</div>
						</template>
					</BaseUiAccordionPanel>	
					<BaseUiAccordionPanel id="desc" active v-if="item?.content">
						<!-- description -->
						<template #header><BaseCmsLabel code='tab_product_description' /></template>
						<template #body>
							<div class="cd-tab-content cd-tab-content-desc">
								<BaseUiToggleContent :content="item.content" :limit="1000" v-slot="{content, contentLength, active, onToggle}">
									<div v-html="content" v-interpolation />
									<div v-if="contentLength > 1000" class="cd-tab-more cd-tab-more-attr" @click="onToggle">{{ active ? labels.get('show_less') : labels.get('show_more')}}</div>
								</BaseUiToggleContent>
							</div>
						</template>
					</BaseUiAccordionPanel>
				</BaseUiAccordion>

				<ClientOnly>
					<CatalogProductsWidget
						:fetch="{related_code: 'related', related_item_id: item.id, related_widget_data: item.related_widget_data, only_available: true, limit: 10, always_to_limit: true, response_fields: config.catalog.productsResponseFields}"
						:frosmo="item?.manufacturer_code == 'dyson-9231' ? 'cd_p1_brand_dyson' : 'cd_p1'"
						thumb-preset="catalogEntry"
						v-slot="{items}">
						<div class="related-products" v-if="items?.length">
							<CmsSectionHeader>
								<BaseCmsLabel code="related_products" />
							</CmsSectionHeader>
							<UiSwiper
								name="relatedProducts"
								:options="{
									slidesPerView: 4,
									slidesPerGroup: 4,
									spaceBetween: 24
								}">
								<BaseUiSwiperSlide v-for="(item, index) in items" :key="item.id">
									<CatalogIndexEntry :item="item" :index="index" :add-to-cart="true" class="cp-related" />
								</BaseUiSwiperSlide>
							</UiSwiper>
						</div>
					</CatalogProductsWidget>
				</ClientOnly>

				<!-- manufacturer description -->
				<BaseUiAccordion class="cd-tab cd-tab-desc" v-if="item?.check_lists?.webcatalog_157271 || item?.check_lists?.webcatalog_201949 || item?.check_lists?.webcatalog_157270">
					<BaseUiAccordionPanel id="state" active>
						<!-- product state -->
						<template #header><BaseCmsLabel code="tab_product_manufacturer_desc" /></template>
						<template #body>
							<div class="cd-tab-content cd-tab-syndicate" :class="{'active': toggleSyndicate}">
								<CatalogSyndicateContent :item="item" />
							</div>
							<div class="cd-tab-more cd-tab-more-attr" @click="toggleSyndicate = !toggleSyndicate">{{ toggleSyndicate ? labels.get('show_less') : labels.get('show_more')}}</div>
						</template>
					</BaseUiAccordionPanel>
				</BaseUiAccordion>

				<ClientOnly>
					<div id="feedback" v-if="item?.feedback_comment_widget?.can_comment || item?.feedback_comment_widget?.items?.length">
						<LazyCatalogProductFeedback :item="item" v-if="item?.feedback_comment_widget?.can_comment || item?.feedback_comment_widget?.items?.length" />
					</div>
				</ClientOnly>
			</div>

			<div class="cd-col2">
				<CatalogProductContainer>
					<div v-if="item.manufacturer_title" class="cd-brand">
						<NuxtLink :to="item.manufacturer_url_without_domain">{{ item.manufacturer_title }}</NuxtLink>
					</div>
					<h1 class="cd-title">{{ item.seo_h1 }}</h1>

					<!-- ratings/comments/code -->
					<div class="cd-info">
						<LazyBaseFeedbackRatesWidget :data="item.feedback_rate_widget" v-slot="{stars}">
							<div class="cd-rate-section" v-if="item?.feedback_rate_widget && item?.feedback_rate_widget?.rates_votes > 0">
								<div v-if="mobileBreakpoint" class="cd-rate-average">{{ Number(item.feedback_rate_widget.rates).toFixed(1) }}</div>
								<FeedbackStars class="cd-rate" :stars="stars" />
								<div v-if="!mobileBreakpoint" class="cd-rate-average">({{ Number(item.feedback_rate_widget.rates).toFixed(1) }})</div>
								<div class="cd-rate-votes" @click="scrollTo('#feedback', {offset: 100})">{{ item.feedback_rate_widget.rates_votes }} <template v-if="!mobileBreakpoint"><BaseCmsLabel code='review' /></template></div>
							</div>
						</LazyBaseFeedbackRatesWidget>
						<div class="cd-code"><BaseCmsLabel code='sku' />:{{ item.code }}</div>
					</div>

					<CatalogProductBadges :item="item" :types="['discount', 'uau', 'condition', 'text']" class="cd-badges" />
					<CatalogProductPrice :item="item" class="cd-price" />
					<div v-if="installmentPrice" class="cd-price-installment">
						<strong>
							<BaseCmsLabel code="installments_price_text" :replace="[{'%PRICE%': formatCurrency(installmentPrice)}]" :strip-html="true" />
						</strong>
						<span @click="modal.open('flyout', {mode: 'installments', header: true, content: {installment_calculation: item.installments_calculation, installment_list: item.installments_list_data}})"><BaseCmsLabel code="read_more" /></span>
					</div>

					<CatalogDynamicPrice :item="item" />
					
					<CatalogProductServices mode="badges" :item="item" />
					<CatalogProductServices :item="item" v-model="selectedServices" />

					<CatalogProductStatus :item="item" />

					<template v-if="!item.temporary_unavailable">
						<template v-if="item.is_available">
							<div class="cd-add-to-cart">
								<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" v-if="item.is_available" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: 1, services: selectedServices}">
									<button class="btn cd-btn-add" @click="onAddToCart()" :disabled="loading || (item.xxx && !isAdult)">
										<UiLoader v-if="loading" mode="dots" />
										<BaseCmsLabel v-else code='cd_add_to_shopping_cart' tag="span" />
									</button>
								</BaseWebshopAddToCart>
							</div>
						</template>
						<template v-else-if="item.feedback_notification_widget">
							<div class="cd-add-to-cart">
								<!-- FIXME INTEG dizajn?
								<FeedbackNotificationForm :itemStatus="item.status" />
								-->
							</div>
						</template>
					</template>

					<CatalogProductSellerDisclaimer :item="item" />
					<CatalogProductExtraInfo :item="item" />
				</CatalogProductContainer>

				<!-- shipping info -->
				<CatalogProductShipping :item="item" />

				<!-- sellers -->
				<CatalogProductContainer v-if="item.seller_id && firstOtherOffer && (!firstMainOffer || item.offers_other_total != 1)" class="icon store flyout" @click="flyoutOpen('seller', {'offers': item.offers, 'id': item.offer_id})">
					<template #title><BaseCmsLabel code="tab_seller_items" /></template>
					<template #content>
						<div class="cd-seller-desc cd-flyout-btn" v-html="offersDescl"></div>

						<div v-if="labels.get('shop_extra_info_pdp') != 'shop_extra_info_pdp'" class="sellers-extra-info" @click="modal.open('flyout', {header: true, title: labels.get('shop_extra_info_flyout_title_pdp'), content: labels.get('shop_extra_info_flyout_content_pdp')})">
							<span v-html="labels.get('shop_extra_info_pdp')"></span>
						</div>
					</template>
				</CatalogProductContainer>

				<!-- energy box -->	
				<CatalogProductContainer class="icon energy" :class="{'flyout': item.energy_image_upload_path}" v-if="energyAttr" @click="item.energy_image_upload_path && modal.open('flyout', {header: true, title: labels.get('energy_flyout_title'), headerIcon: 'energy', content: '<img src='+absolute(item?.energy_image_upload_path)+' />'})">
					<template #title><BaseCmsLabel code="energy_title" /></template>
					<template #content>
						<div class="cd-energy-content">
							<BaseUiImage v-if="energyAttr.image_upload_path" :src="energyAttr.image_upload_path" width="40" height="22" default="/images/no-image-50.jpg" :alt="energyAttr.title" />
							<BaseCmsLabel code='energy_info' />
						</div>
					</template>
				</CatalogProductContainer>

				<CatalogProductContainer v-if="item?.seller_code == '9999' && labels.get('companies_value') != 'companies_value'">
					<template #title><BaseCmsLabel code="companies_value" /></template>
					<template #content>
						<div class="companies" v-if="labels.get('companies_info') != 'companies_info'" v-html="labels.get('companies_info')"></div>
					</template>
				</CatalogProductContainer>
			</div>
			<CatalogAgeVerificationOverlay :item="item" class="cp-detail-age-verification" />
		</div>

		<!-- Related products -->
		<ClientOnly>
			<CatalogProductsWidget
				:fetch="{related_code: 'related', related_item_id: item.id, related_widget_data: item.related_widget_data, only_available: true, limit: 10, always_to_limit: true, response_fields: config.catalog.productsResponseFields}"
				:frosmo="item?.manufacturer_code == 'dyson-9231' ? 'cd_p2_brand_dyson' : 'cd_p2'"
				thumb-preset="catalogEntry"
				v-slot="{items}">
				<div class="cw" v-if="items?.length">
					<div class="wrapper">
						<CmsSectionHeader>
							<BaseCmsLabel code="similar_products" />
						</CmsSectionHeader>
					</div>
					<UiSwiper
						name="relatedProducts2"
						:options="{
							enabled: false,
							slidesPerView: 5,
							slidesPerGroup: 5,
							spaceBetween: 24,
							breakpoints: {
								980: {
									enabled: true,
								}
							}
						}">
						<BaseUiSwiperSlide v-for="(item, index) in items" :key="item.id" class="slide">
							<CatalogIndexEntry :item="item" :index="index" />
						</BaseUiSwiperSlide>
					</UiSwiper>
				</div>
			</CatalogProductsWidget>
		</ClientOnly>
	</BaseCatalogDetail>
	<ClientOnly>
		<Teleport to="body">
			<CatalogCompare />
		</Teleport>
	</ClientOnly>	
</template>

<script setup>
	const webshop = useWebshop();
	const config = useAppConfig();
	const endpoints = useEndpoints();
	const route = useRoute();
	const labels = useLabels();
	const {emit} = useEventBus();
	const {generateThumbs} = useImages();
	const {scrollTo} = useDom();
	const {formatCurrency} = useCurrency();
	const {getLastUrlSegment, absolute} = useUrl();
	const modal = useModal();
	const item = useState('product');
	const {openAgeModal, isAdult} = useAgeVerification();
	const {isLoyalty, installmentPrice} = useProductData(item.value);

	const selectedServices = ref([]);

	/*
	FIXME:
	- xxx age verification
	*/

	//rwd
	const {mobileBreakpoint} = inject('rwd');

	//remove script
	/*
	function removeScript() {
		// Find the script by id and remove it
		const script = document.getElementById('4thvision');
		if (script) {
			script.remove();
		}
	}
	*/

	// generate thumbnails
	async function imageThumbs() {
		return Promise.all([
			generateThumbs({
				data: item.value,
				preset: 'catalogDetail'
			}),
			generateThumbs({
				data: item.value,
				preset: 'catalogEnergy'
			}),
		]);
	}

	/* 
		Filter out unwanted attributes. Group same attribute values into one attribute. Data structure:
		{
			"Boja": {
				"values": ["Crvena", "Zelena"],
				"unit": ""
			}
		}
	*/
	const itemsAttributes = computed(() => {
		if (!item?.value?.attributes) return null;

		return item.value.attributes.reduce((acc, attr) => {
			if (['attribute_badges', 'superbenefits', 'katalog-9001'].includes(attr.code)) return acc;

			const key = attr.attribute_title;
			if (!acc[key]) {
				acc[key] = { values: [], unit: attr.attribute_unit || '' };
			}
			acc[key].values.push(attr.title);
			return acc;
		}, {});
	});

	// Toggle between showing all attributes and only the first 6
	const showAttr = ref(false);
	const displayedAttributes = computed(() => {
		const attributesArray = Object.entries(itemsAttributes.value || {});
		if(!attributesArray.length) return null;

		if (showAttr.value) {
			return attributesArray.map(([title, value]) => ({ title, ...value }));
		} else {
			return attributesArray.slice(0, 6).map(([title, value]) => ({ title, ...value }));
		}
	});

	//manufacturer content
	const toggleSyndicate = ref(false);
	

	//shipping info
	//FIXME INTEG jezik
	

	//sellers
	const firstOtherOffer = computed(() => {
		return item?.value?.first_other_offer_id ? item?.value?.offers?.find(offer => offer.id === item?.value?.first_other_offer_id.toString()) : null;
	});
	const firstMainOffer = computed(() => {
		return item?.value?.first_main_seller_offer_id ? item?.value?.offers?.find(offer => offer.id === item?.value?.first_main_seller_offer_id.toString()) : null;
	});
	const offersFlyoutTitle = computed(() => {
		let text = labels.get('offers_descl');
		if (item?.value?.offers_other_total === 1) {
			text = labels.get('offers_1_descl');
		} else if (item?.value?.offers_other_total === 2) {
			text = labels.get('offers_2_descl');
		} else if (item?.value?.offers_other_total === 3 || item?.value?.offers_other_total === 4) {
			text = labels.get('offers_3_descl');
		}

		return labels
			.get('seller_flyout_title')
			.replace('%s%', item?.value?.offers_other_total ?? 0)
			.replace('%offers_descl%', text);
	});
	const offersDescl = computed(() => {
		let text = labels.get('offers_descl');
		if (item?.value?.offers_other_total === 1) {
			text = labels.get('offers_1_descl');
		} else if (item?.value?.offers_other_total === 2) {
			text = labels.get('offers_2_descl');
		} else if (item?.value?.offers_other_total === 3 || item?.value?.offers_other_total === 4) {
			text = labels.get('offers_3_descl');
		}

		return labels
			.get('seller_others')
			.replace('%offers_total%', item?.value?.offers_other_total ?? 0)
			.replace('%offers_descl%', text)
			.replace('%price%', firstOtherOffer.value.price_custom ?? 0);
	});

	//energy
	const energyAttr = computed(() => {
		const targetAttributeCodes = ['ucinek-pranja-in-su-100218739', 'razred-energijske-u-100215480', 'razred-energijske-u-100176542', 'razred-energij-ucinkov-35'];
		return item?.value?.attributes_special?.find(attr => targetAttributeCodes.includes(attr.attribute_code)) || null;
	});

	//flyout open
	function flyoutOpen(mode, value) {
		//seller
		if(mode == 'seller') {
			emit('flyoutUpdate', {'flyout_mode': 'sellers', 'flyout_title': offersFlyoutTitle, 'flyout_content': value && value.offers ? value.offers : null, 'flyout_offer_id': value && value.id ? value.id : null});
		}
	}
</script>

<style lang="less" scoped>
	//tabs
	:deep(.accordion-panel){
		background: var(--white); border-radius: 12px; margin-bottom: 24px; 
		&.active{
			.accordion-panel-header:after{.rotate(180deg);}
			.accordion-panel-body{padding-bottom: 24px;}
		}
	}
	:deep(.accordion-panel-header){
		display: flex; align-items: center; font-size: 24px; line-height: 1.4; font-weight: 600; color: var(--black); position: relative; cursor: pointer; padding: 17px 24px;
		&:after{.icon-arrow-down(); font: 12px/1 var(--fonti); font-weight: normal; color: var(--black); margin-left: auto;}
		&.no-icon{
			cursor: default;
			&:before{content: none;}
		}

		@media (max-width: @m){
			height: 55px; font-size: 18px;
			&:before{font-size: 10px;}
			&.active:before{.rotate(180deg);}
		}
	}
	:deep(.accordion-panel-body){padding: 0 24px;}
	.cd-tab-more{
		display: inline-flex; font-size: 12px; text-decoration: underline; cursor: pointer; margin-top: 15px;
		@media (min-width: @t){
			&:hover{text-decoration: none;}
		}
	}

	//main
	.cd-bc{
		padding-bottom: 32px;
		
		@media (max-width: @m){display: none;}
	}
	.cd-main{
		display: flex; margin-bottom: 58px; position: relative; gap: var(--elementGap);

		@media (max-width: @m){flex-direction: column-reverse; margin: 0 0 12px;}
	}

	.companies{
		:deep(a){
			color: var(--textColor); text-underline-offset: 3px; text-decoration-thickness: 1px; position: relative; padding-right: 18px;
			&:after{.icon-link2(); position: absolute; bottom: 3px; right: 0; font: 13px/1 var(--fonti);}
		}
	}
	.cd-col1{
		width: 952px; flex-shrink: 0;

		@media (max-width: @l){width: 700px;}
		@media (max-width: @t){width: 550px;}
		@media (max-width: @m){width: 100%;}
	}

	//attribute
	.cd-tab-attributes{
		display: grid; grid-template-columns: 1fr 1fr; gap: var(--elementGap); font-size: 14px; line-height: 1.2; position: relative;
		&:before{.pseudo(1px,auto); background: var(--gray2); position: absolute; left: 50%; top: 0; bottom: 12px; z-index: 1;}

		@media (max-width: @m){
			display: block; gap: 14px; font-size: 12px;
			&:before{content: none;}
		}
	}
	.cd-tab-attribute{
		display: flex;

		@media (max-width: @m){
			margin-bottom: 14px;
			&:last-child{margin-bottom: 0;}
		}
	}
	.cd-tab-attribute-title{width: 50%; flex-shrink: 0; padding-right: 30px; color: #6D6D6D;}
	.cd-tab-attribute-value{width: 50%;}

	//description
	.cd-tab-content-desc{
		font-size: 14px; line-height: 1.5;
		:deep(ul), :deep(ol){margin: 0 0 24px 20px;}
		:deep(h2), :deep(h3), :deep(h4), :deep(h5){padding: 15px 0 10px; font-weight: 600;}
		:deep(iframe), :deep(video){width: 100%;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%;}
		:deep(p:last-child){padding-bottom: 0;}
	}
	.cd-tab-more-desc{margin-top: 24px;}
	.cd-tab-syndicate{
		max-height: 500px; overflow: hidden;
		&.active{max-height: none;}
	}



	//main info
	.cd-col2{flex-grow: 1; display: flex; flex-direction: column; gap: 24px;}
	.sellers-extra-info{
		margin-top: 15px; font-size: 14px; padding-left: 20px; position: relative; text-decoration: underline; cursor: pointer; text-underline-offset: 3px; text-decoration-thickness: 1px; position: relative;
		&:before{.icon-info(); font: 13px/1 var(--fonti); position: absolute; left: 0; top: 3px;}
	}
	.cd-brand{
		display: flex; margin-bottom: 6px; font-size: 14px;
		a{text-decoration: underline; color: var(--black);}
		@media (min-width: @t){
			a:hover{text-decoration: none;}
		}
	}
	.cd-title{
		display: block; padding: 0 0 12px; font-size: 20px; line-height: 1.3;
	}

	.cd-info{display: flex; align-items: center; padding-bottom: 15px;}
	.cd-rate-section{display: flex; align-items: flex-end; flex-grow: 1; padding-right: 20px; font-size: 11px; font-weight: 300; color: var(--black);}
	
	.cd-rate-average{
		margin-left: 3px;

		@media (max-width: @m){margin: 0 4px 0 0; font-size: 12px; line-height: 1.2; font-weight: 600;}
	}
	.cd-rate-votes{
		margin-left: 5px; text-decoration: underline; cursor: pointer;
		&:hover{text-decoration: none;}

		@media (max-width: @m){margin-left: 4px;}
	}
	.cd-code{display: block; font-size: 12px; color: var(--gray5);}

	//price
	.cd-price{
		display: flex; align-items: center; padding-top: 25px; padding-bottom: 7px; gap: 10px; row-gap: 7px;
		.red{color: var(--errorColor);}

		@media (max-width: @m){align-items: baseline}
	}
	:deep(.current-price){
		font-size: 28px; font-weight: 700;
		:deep(.p-comma){display: none;}
		:deep(.p-d){font-size: 16px; vertical-align: text-top;}
	}
	:deep(.old-price){
		font-size: 14px; padding: 0;
	}
	.cd-price-info{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 15px; height: 15px; margin-left: 8px; position: relative;
		&:before{.icon-info(); font: 15px/1 var(--fonti); color: var(--black);}
		&:hover .cd-price-tooltip{display: flex;}
	}
	.cd-price-tooltip{
		display: none; align-items: center; white-space: nowrap; padding: 6px 8px; border-radius: 6px; background: var(--gray3); font-size: 11px; font-weight: normal; color: var(--black); position: absolute; right: -10px; bottom: calc(~"100% - -8px");
		&:before{.pseudo(8px,8px); background: var(--gray3); position: absolute; top: calc(~"100% - 5px"); right: 12px; z-index: 1; .rotate(45deg);}
	}
	.cd-price-installment{
		display: block; font-size: 14px; padding-bottom: 5px; font-weight: bold; display: flex; gap: 7px;
		:deep(p){padding: 0;}
		span{
			font-weight: normal; text-decoration: underline; cursor: pointer;
			&:hover{text-decoration: none;}
		}
	}

	//dynamic price
	.cd-conf-price-label{
		display: none; margin-top: 8px; font-size: 14px; color: var(--black);
		&.active{display: block;}
		:deep(a){
			color: var(--black);
			&:hover{text-decoration: none;}
		}
	}
	.cd-conf-price-section{width: 100%; margin-top: 12px;}
	.cd-conf-price-header{display: flex; align-items: center; margin-bottom: 6px;}
	.cd-conf-price-title{flex-grow: 1; font-size: 14px; color: var(--errorColor);}
	.cd-conf-price-remove{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 20px; height: 20px; background: var(--gray3); border-radius: 100%; font-size: 0; line-height: 0; text-decoration: none; position: relative; .transition(background); cursor: pointer;
		&:before{.icon-x(); font: 10px/1 var(--fonti); color: var(--black); font-weight: 600; text-indent: 1px;}
	}
	.cd-conf-price{
		display: flex; align-items: center; flex-grow: 1;
		span{display: block;}
		.cd-current-price{flex-grow: 1; flex-shrink: 0; color: var(--errorColor);}
	}
	.cd-conf-price-timer{display: flex; align-items: center; flex-shrink: 0; font-size: 15px; color: var(--gray5); position: relative;}
	.cd-conf-price-timer-bottom{
		display: flex; align-items: center;
		.countdown{
			display: flex; flex-shrink: 0;
			:deep(&>span){display: flex; justify-content: center; min-width: 36px; margin-left: 12px; position: relative;}
		}
		:deep(.value){font-size: 28px; font-weight: 600; color: var(--errorColor); position: relative;}
		:deep(.frame){font-size: 8px; font-weight: 400; text-align: center; color: var(--gray5); position: absolute; bottom: 0; top: 100%;}
	}
	.cd-conf-price-note{display: block; width: 100%; margin-top: 22px; font-size: 10px; color: var(--gray5); position: relative;}

	//add to cart
	.cd-add-to-cart{display: flex; justify-content: center; margin-top: 24px;}
	.cd-btn-add{
		width: 100%; height: 60px; border-radius: 30px; font-size: 18px;
		span{
			padding-left: 32px; position: relative;
			&:before{.icon-cart(); font: 21px/1 var(--fonti); color: var(--white); position: absolute; left: 0;}
		}

		@media (max-width: @m){height: 52px; border-radius: 26px; font-size: 15px;}
	}

	.cw{
		margin-bottom: 55px; position: relative;
		:deep(.swiper){overflow: initial;}
	}

	.related-products{
		padding: 5px 0 50px;
		.section-header{padding-left: 20px;}
		:deep(.swiper-navigation){
			left: auto; bottom: auto; display: flex; gap: 20px; top: -67px; right: 20px;
			.swiper-button-next{right: auto;}
			.swiper-button{
				position: relative; top: auto; left: auto; transform: none; width: 46px; height: 46px; background: #fff;
				&:before{font-size: 13px; color: var(--gray7);}
				&.swiper-button-disabled{opacity: 0.5;}
			}
		}
	}

	//energy
	.cd-energy-content{
		display: flex; align-items: center;
		:deep(img){display: block; margin-right: 12px; width: auto; height: auto; max-width: 40px; max-height: 22px;}
	}

	.age-verification{
		.cd-container, .cd-tab{
			*, &:before, &:after{filter: blur(4px);}
		}
	}
</style>