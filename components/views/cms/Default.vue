<template>
	<BaseCmsPage v-slot="{page}">
		<div class="wrapper">
			<CmsBreadcrumbs class="bc-contact" :items="page?.breadcrumbs || []" />
			
			<CmsCardLarge class="only-image" v-if="page?.main_image_upload_path">
				<template #header>
					<BaseUiImage :data="page?.main_image_thumbs?.['width1440-height530-crop1']" default="/images/no-image.jpg" />
				</template>
			</CmsCardLarge>

			<div class="wrapper2">
				<CmsContent v-if="page?.content">
					<h1 v-if="page?.title">{{ page?.title }}</h1>
					<div v-html="page.content" v-interpolation />
				</CmsContent>
			</div>
		</div>
	</BaseCmsPage>
</template>