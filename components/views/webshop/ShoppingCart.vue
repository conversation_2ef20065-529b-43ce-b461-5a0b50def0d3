<template>
	<BaseCmsPage v-slot="{page}">
		<div class="page">
			<BaseWebshopCart thumb-preset="cart" v-slot="{cart, parcels, loading}">
				<div class="wrapper">
					<h1 v-html="page?.seo_h1"></h1>
				</div>
				<ClientOnly>
					<div v-if="parcels?.length && !loading">
						<div class="wrapper">
							<div class="w-row">
								<div class="w-col1">
									<WebshopErrorMessage :data="parcels" :items="cart?.errors" :warnings="cart?.warnings" />
									<div class="carts">
										<div class="cart" v-for="parcel in parcels" :key="parcel.number">
											<div class="cart-title">
												{{ parcel.number }}. <span class="cart-parcel"><BaseCmsLabel code="seller_item_title" />&nbsp;</span>
												<template v-if="parcel.parcel_name">{{ parcel.parcel_name }}</template>
											</div>
											<div class="cart-items">
												<WebshopCartItem v-for="item in parcel.items" :data="item" :parcel="parcel" :key="item.id" />	
											</div>
										</div>
									</div>
								</div>

								<div class="w-col2">
									<WebshopSidebar />
									<!--
									<WebshopWidgetFixedbar mode="cart" />
								-->
								</div>
							</div>
						</div>
					</div>
					<LazyWebshopEmptyCart v-if="!parcels?.length && !loading" />
					<template v-if="loading">
						<UiLoader class="spacing" />
					</template>					
					<template #fallback>
						<UiLoader class="spacing" />
					</template>
				</ClientOnly>
			</BaseWebshopCart>
		</div>
	</BaseCmsPage>
</template>

<style lang="less" scoped>
	.page{
		padding: 25px 0 50px;
		@media (max-width: @m){padding: 20px 0 0;}
	}
	.wrapper{padding: 0;}
	.w-row{
		display: flex; gap: 25px;
		@media (max-width: @m){flex-direction: column; gap: 12px;}
	}
	.w-col1{flex: 1;}
	.w-col2{
		flex: 0 0 clamp(360px, 30vw, 460px);
		@media (max-width: @m){width: auto; flex: none; margin: 0 calc(var(--wrapperMargin) * -1);}
	}
	h1{font-size: 24px; font-weight: bold; padding: 0 0 clamp(10px, 2vw, 30px);}

	.carts{display: flex; flex-direction: column; gap: clamp(12px, 2vw, 20px);}
	.cart{background: #fff; border-radius: var(--borderRadius); padding: clamp(15px, 2vw, 20px) 15px clamp(5px, 2vw, 25px);}
	.cart-title{font-size: clamp(16px, 2vw, 18px); font-weight: bold; padding: 0 0 clamp(5px, 2vw, 25px);}
</style>