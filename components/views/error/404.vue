<template>
	<BaseCmsPage :fetch="{slug: '/404/'}" v-slot="{page}">
		<div class="wrapper">
			<div class="wrapper2">
				<CmsContent v-if="page.content">
					<h1 v-if="page?.seo_h1">{{ page?.seo_h1 }}</h1>
					<div v-html="page.content" v-interpolation />
					<p>
						<NuxtLink class="btn" to="/"><BaseCmsLabel code="return_back" /></NuxtLink>
					</p>
				</CmsContent>
			</div>
		</div>
	</BaseCmsPage>
</template>

<style scoped lang="less">
	@media (max-width: @m){
		.content{margin-top: 0;}
	}
</style>