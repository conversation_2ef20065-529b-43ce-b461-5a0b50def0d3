<template>
	<div class="set-wishlist-cnt" :class="[mode]">
		<BaseCatalogSetWishlist v-slot="{active, onToggleWishlist, loading}" :item="item">
			<div class="set-wishlist" :class="[mode, {'active': active, 'loading': loading}]" @click="onToggleWishlist()">
				<span class="loader" v-show="loading"><UiLoader class="inline" /></span>
				<BaseCmsLabel v-if="!active" code="add_to_wishlist" />
				<BaseCmsLabel v-else code="remove_from_wishlist" />
			</div>
		</BaseCatalogSetWishlist>
	</div>
</template>

<script setup>
	const props = defineProps(['item','mode']);
</script>

<style lang="less" scoped>
	.set-wishlist{
		display: flex; align-items: center; color: var(--gray5); justify-content: center; flex-shrink: 0; font-size: 0; width: 36px; height: 36px; background: rgba(255, 255, 255, 0.90); border-radius: 100%; text-decoration: none; cursor: pointer;
		&:before{width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; .icon-wishlist(); font: 22px/1 var(--fonti); color: var(--gray5); margin-top: 2px;}
		&.active{
			color: var(--blueDark);
			&:before{.icon-wishlist-full(); color: var(--blueDark);}
		}
		&.loading:before{display: none;}
		&.detail{width: 46px; height: 46px; box-shadow: 0px 0.5px 2px 0px rgba(0, 0, 0, 0.25);}

		@media (max-width: @m){
			&.detail{
				width: 36px; height: 36px;
				&:before{font-size: 17px;}
			}
		}
	}
	.loader{width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;}
	.list{
		.set-wishlist{width: auto; font-size: 15px; gap: 5px; font-weight: bold;}
	}
	.cart{
		.loader{width: 20px; height: 20px;}
		.set-wishlist{
			width: auto; height: auto; font-size: clamp(13px, 1.5vw, 14px); text-decoration: underline; color: var(--blueDark);
			@media (max-width: @t){justify-content: start; gap: 7px;}
			&:before{display: none;}
		}
	}
</style>