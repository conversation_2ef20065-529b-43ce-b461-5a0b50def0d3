<template>
	<article class="cp">
		<div class="cp-col cp-col1">
			<div class="cp-thumbs" v-if="item.other_images?.length">
				<NuxtLink :to="item.url_without_domain" class="cp-image-thumb" v-for="image in item.other_images" :key="image.id">
					<BaseUiImage loading="lazy" :data="image.file_upload_path_thumb_small" default="/images/no-image.jpg" />
					<!--<BaseUiImage loading="lazy" :data="image.image_thumbs?.['width110-width110']" default="/images/no-image.jpg" />-->
				</NuxtLink>
			</div>
			<figure class="cp-image">
				<NuxtLink :to="item.url_without_domain" class="cp-image-main">
					<BaseUiImage loading="lazy" :data="item.main_image_upload_path_thumb" default="/images/no-image.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : item.title" />
					<!--<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width400-height400']" default="/images/no-image.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : item.title" />-->
				</NuxtLink>
			</figure>
		</div>
		<div class="cp-col cp-col2">
			<div class="cp-title">
				<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
			</div>
			<ClientOnly>
				<CatalogRates :item="item" />
				<CatalogProductBadges :item="item" :types="['energy', 'discount', 'uau', 'condition', 'text']" class="text-badges" />
			
				<div v-if="item.short_description && stripHtml(item.short_description) != item.title" class="cp-description" v-html="limitWords(stripHtml(item.short_description), 25, '...')"></div>

				<CatalogProductBadges :item="item" :types="['image']" class="cp-badges-special" />
			</ClientOnly>
		</div>
		<div class="cp-col cp-col3">
			<div class="cp-info">
				<ClientOnly>
					<CatalogProductPrice :item="item" />
					<CatalogProductInfo class="cp-list-product-info" :item="item" />
					<div class="cp-list-toolbar">
						<CatalogSetCompare :item="item" mode="list" />
						<CatalogSetWishlists :item="item" mode="list" />
					</div>
				</ClientOnly>
			</div>
			<template v-if="installmentPrice">
				<div class="or"><span>{{ lang.get() === 'hr' ? 'ili' : 'ali' }}</span></div>
				<div class="cp-payments">
					<div class="cp-payments-price-cnt">
						<div class="cp-payments-price">
							<BaseUtilsFormatCurrency :wrap="true" :price="installmentPrice" /> / {{ lang.get() === 'hr' ? 'mj' : 'na mesec' }}.
						</div>
						<div><BaseCmsLabel code="installments_info" :replace="[{'%VALUE%': maxInstallments}]" /> <span class="cp-payments-price-more" @click="modal.open('flyout', {mode: 'installments', header: true, content: {installment_calculation: item.installments_calculation, installment_list: item.installments_list_data}})"><BaseCmsLabel code="read_more" /></span></div>
					</div>
				</div>
			</template>
		</div>
		<CatalogAgeVerificationOverlay :item="props.item" class="cp-list-age-verification" />
	</article>
</template>

<script setup>
	const labels = useLabels();
	const lang = useLang();
	const {formatCurrency} = useCurrency();
	const props = defineProps(['item']);
	const {installmentPrice, imageBadges, textBadges, maxInstallments} = useProductData(props.item);
	const modal = useModal();
	const {stripHtml, limitWords} = useText();
</script>

<style lang="less" scoped>
	.cp{
		display: flex; background: var(--white); border: 1px solid var(--white); border-radius: var(--borderRadius); position: relative; .transition(border-color); padding: 15px; gap: 10px;
		&:hover{border-color: var(--blueDark);}
	}
	.cp-col1{width: 455px; flex-grow: 0; flex-shrink: 0; display: flex; gap: 10px;}
	.cp-thumbs{width: 75px; flex-grow: 0; display: flex; flex-direction: column; flex-shrink: 0; gap: 10px; justify-content: center;}
	.cp-image-thumb{
		width: 100%; aspect-ratio: 1; display: flex; align-items: center; justify-content: center; background: var(--gray3); padding: 5px; overflow: hidden; border-radius: 6px;
		:deep(img){display: block; max-height: 100%; width: auto; height: auto; mix-blend-mode: darken;}
	}
	.cp-image{
		display: flex; align-items: center; justify-content: center; flex-grow: 1;
		:deep(img){max-width: 260px; max-height: 260px; width: auto; height: auto;}
	}
	.cp-col2{flex-grow: 1; padding: 10px 80px 0 0; display: flex; flex-direction: column;}
	.cp-title{
		padding: 0 0 8px;
		a{text-decoration: none; color: var(--textColor);}
	}
	:deep(.cp-rate){padding: 0 0 10px;}
	.cp-description{font-size: 12px; line-height: 1.4;}
	
	.cp-energy{
		:deep(img){max-height: 22px; width: auto;}
	}
	.cp-col3{flex: 0 0 470px; display: flex;}
	.cp-info{flex-grow: 1;}
	.or{
		width: 1px; background: var(--gray2); font-size: 12px; position: relative;
		span{padding: 6px 0; width: 20px; display: block; text-align: center; position: absolute; background: #fff; left: -10px; top: 50%; transform: translateY(-50%);}
	}
	.cp-payments{width: 190px; flex-shrink: 0; flex-grow: 0; padding: 0 10px 0 30px; text-align: center; display: flex; align-items: center; justify-content: center; font-size: 12px; color: var(--gray5); text-wrap: balance;}
	.price{padding: 10px 0 60px; align-items: center;}
	.cp-payments-price{
		color: var(--textColor); padding-bottom: 5px;
		span{font-weight: bold; font-size: 22px;}
	}
	:deep(.cp-old-price){padding: 0;}
	:deep(.cp-price-info){margin-bottom: 2px;}
	.cp-list-toolbar{display: flex; position: absolute; bottom: 15px; margin-left: -8px;}
	.text-badges{position: relative; bottom: auto; left: auto; right: auto; padding-bottom: 10px;}
	:deep(.cp-energy){width: auto; order: 100;}
	.cp-badges-special{
		margin-top: auto; display: flex; gap: 10px; align-items: center; padding-top: 20px; padding-bottom: 5px;
		:deep(img){display: block;}
	}
	.cp-outlet{padding-top: 15px; color: var(--textColor);}
	.cp-payments-price-more{color: var(--blueDark); cursor: pointer;}
	.cp-list-product-info{padding-bottom: 50px;}
</style>
