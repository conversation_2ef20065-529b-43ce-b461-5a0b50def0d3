<template>
	<div v-if="props.item?.check_lists?.[webcatalog3D]" class="cd-model-view" @click="onOpen()">
		<BaseCmsLabel code="view_3d" tag="span" />
	</div>
	<ClientOnly>
		<teleport to="body">
			<div class="viewer" v-if="open3dViewer">
				<div class="close" @click="onClose()">X</div>
				<bb-3dviewer id="3dviewer" :sku="item.code"></bb-3dviewer>
			</div>
		</teleport>
	</ClientOnly>
</template>

<script setup>
	const config = useAppConfig();
	const {addScript, uninstallScript} = useMeta();
	const labels = useLabels();
	const props = defineProps(['item']);
	const origin = ['https://www.bigbang.si', 'https://beta.bigbang.si'].includes(config.host) ? true : false;
	const qrUrl = config.host == 'https://www.bigbang.si' ? 'https://bb.4th.vision/prod/index.html?sku=' : 'https://bb.4th.vision/test/index.html?sku=';
	const webcatalog3D = origin ? 'webcatalog_1219026' : 'webcatalog_2225499';
	const open3dViewer = ref(false);

	let viewerTimeout;
	function onOpen() {
		if(viewerTimeout) clearTimeout(viewerTimeout);
		open3dViewer.value = true;

		addScript({
			src: config.host == 'https://www.bigbang.si' ? labels.get('prod_3dviewer') : labels.get('dev_3dviewer'),
			type: 'module',
			key: '4thvision',
			async: true,
		});

		viewerTimeout = setTimeout(() => {
			const viewer = document.getElementById("3dviewer");
			/*
			viewer.addEventListener("error", (error) => {
				console.log("error", error);
			});
			viewer.addEventListener("load", (event) => {
				console.log("load", event.detail.loaded);
			});
			*/
			viewer.setAttribute("qr-url", qrUrl + props.item.code);
		}, 400);
	}

	function onClose() {
		open3dViewer.value = false
		uninstallScript({
			key: '4thvision',
		});
	}

	onUnmounted(() => {
		onClose();
	});
</script>

<style scoped lang="less">
	.cd-model-view{
		display: flex; align-items: center; justify-content: center; height: 40px; padding: 5px 15px; background: var(--white); border-radius: 100px; font-size: 16px; font-weight: 500; color: var(--gray5); position: absolute; right: 16px; bottom: 16px; box-shadow: 0px 0.764px 3.055px 0px rgba(0, 0, 0, 0.25); z-index: 1; cursor: pointer;
		span{
			padding-left: 34px; position: relative;
			&:before{.icon-d(); font: 24px/1 var(--fonti); color: var(--gray5); position: absolute; left: 0;}
		}

		@media (max-width: @m){
			width: 36px; height: 36px; margin-top: 8px; padding: 0; border-radius: 100%; position: relative; top: unset; right: unset; left: unset; bottom: unset; box-shadow: 0px 0.5px 2px 0px rgba(0, 0, 0, 0.25);
			&:before{.icon-d(); font: 22px/1 var(--fonti); color: var(--gray5); position: absolute;}
			span{display: none;}
		}
	}

	.viewer{position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 999999; background: #fff;}
	.close{
		position: absolute; display: flex; align-items: center; justify-content: center; color: var(--gray5); top: 10px; right: 10px; width: 50px; height: 50px; font-size: 0; cursor: pointer; z-index: 999;
		&:before{.icon-x(); font: 20px/1 var(--fonti);}
	}
</style>