<template>
	<!--<iframe :src="'https://media.flixcar.com/delivery/webcall/content/13308/in/mpn/SM-R870NZKAINU/'" frameborder="0"></iframe>-->
	<div v-if="content" id="content" v-html="content"></div>
</template>

<script setup>
	const {addScript, uninstallScript} = useMeta();
	const labels = useLabels();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
	});

	//content
	watch(
		() => props.item,
		async (newValue) => {
			// Reload flix script with new data when configurable product is changed
			uninstallFlix();
			await fetchFlixWebcatalog_157270();
			loadFlix();

			if(props.item?.check_lists && props.item?.check_lists?.webcatalog_201949 == true) {
				window.addEventListener('message', parhelion, false);
			}
		}
	);

	const flixContent = ref(null);
	const content = computed(() => {
		let value = '';

		if (flixContent?.value?.event == 'matchhit') {
			value = '<div/>';
		} else if(props.item.check_lists && props.item.check_lists.webcatalog_157271 == true) {
			value = '<div class="loadbeeTabContent" data-loadbee-apikey="CBTGtm5pktttLraG5zsAZRmvfmKdzbfZ" data-loadbee-gtintype="ean" data-loadbee-gtin="' + props.item.ean_code + '" data-loadbee-locale="sl_SI" data-loadbee-debug="false"></div>';
		} else if(props.item.check_lists && props.item.check_lists.webcatalog_201949 == true) {
			value = '<iframe width="100%" src="https://bigbang.parhelion.hr/?ean=' + props.item.ean_code + '" frameborder="0" scrolling="auto" id="parhelion-frames"></iframe>';
		}

		return value;
	});

	onMounted(async () => {
		// Check if Flix content is available. If it is, install script
		await fetchFlixWebcatalog_157270();
		loadFlix();

		// Install LoadBee
		if(props.item?.check_lists && props.item?.check_lists?.webcatalog_157271 == true) {
			addScript({
				src: 'https://cdn.loadbee.com/js/loadbee_integration.js',
				key: 'loadbee',
			});
		}

		if(props.item?.check_lists && props.item?.check_lists?.webcatalog_201949 == true) {
			window.addEventListener('message', parhelion, false);
		}
	});

	onBeforeUnmount(() => {
		window.removeEventListener('message', parhelion, false);
		uninstallScript({key: 'flix'});
		uninstallScript({key: 'loadbee'});
	});

	// Append flix script to page element where content is rendered (flix is creating its own element)
	function loadFlix() {
		if(flixContent?.value?.event != 'matchhit') return;
		addScript({
			src: 'https://media.flixfacts.com/js/loader.js',
			key: 'flix',
			dataAttributes: {
				'flix-distributor': '10251',
				'flix-language': 'sl',
				'flix-brand': props.item?.manufacturer_title || '',
				'flix-ean': props.item?.ean_code || '',
				'flix-sku': '',
				'flix-button': 'flix-minisite',
				'flix-inpage': 'flix-inpage',
				'flix-button-image': '',
				'flix-fallback-language': 'en',
				'flix-autoload': 'inpage',
				'flix-price': ''
			},
			appendTo: '#external-content'
		});
	}

	function uninstallFlix() {
		uninstallScript({
			key: 'flix',
			onUninstall: () => {
				const el = document.querySelector('#flix-inpage');
				if(el) el.remove();
			}
		});
	}

	// Fetch flix content to check if it exists
	async function fetchFlixWebcatalog_157270() {
		const eanCode = props.item?.ean_code ? props.item.ean_code : '';
		if(eanCode && props.item?.check_lists && props.item?.check_lists?.webcatalog_157270 == true) {
			try{
				flixContent.value = await $fetch(`https://media.flixcar.com/delivery/webcall/match/10251/sl/ean/${eanCode}?resp=0`);
			} catch (e) {
				flixContent.value = null;
			}
		}
	}

	function parhelion(e) {
		if (e.hasOwnProperty('originalEvent')) {
			var origin = e.originalEvent.origin || e.origin;
		} else {
			var origin = e.origin;
		}
		if (origin !== 'https://bigbang.parhelion.hr') return
		let box = document.getElementById('parhelion-frames');
		if(box){
			box.style.height = e.data.frameHeight + 'px';
		}
	}
</script>

<style scoped lang="less">
	.cd-tab-more{
		display: inline-flex; font-size: 12px; text-decoration: underline; cursor: pointer; margin-top: 15px;
		@media (min-width: @t){
			&:hover{text-decoration: none;}
		}
	}
</style>