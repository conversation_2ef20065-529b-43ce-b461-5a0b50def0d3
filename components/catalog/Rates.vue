<template>
	<BaseFeedbackRatesWidget v-if="item?.feedback_rate_widget && +item?.feedback_rate_widget?.rates_votes > 0" :data="item.feedback_rate_widget" v-slot="{stars}">
		<div class="cp-rate rates-container" :class="props.class" v-html="stars" />
	</BaseFeedbackRatesWidget>
</template>

<script setup>
	const props = defineProps(['item', 'class']);
</script>

<style scoped lang="less">
	.cp-rate{
		display: flex; padding-bottom: 18px;
		:deep(.icon-star-empty){
			position: relative; width: 14px; height: 14px; display: flex; align-items: center; justify-content: center;
			&:after{.pseudo(100%,100%); position: relative; background-image: url(assets/images/star.svg); background-size: contain; background-repeat: no-repeat;}
			&.active{
				width: 15px; height: 15px;
				&:after{background-image: url(assets/images/star-yellow.svg);}
			}
		}
	}
</style>