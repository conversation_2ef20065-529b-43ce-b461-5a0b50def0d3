<template>
	<div class="cp-product-info">
		<div class="cp-product-info-item cp-availability" :class="{'unavailable': ['7', '9'].includes(item?.availability_info?.status)}" v-if="item?.availability_info">
			<template v-if="item?.availability_info?.status == 1">
				<span v-if="(item.last_piece_sale != 0 && item.last_piece_sale) && item.warehouses_single_pickup_display" class="available-last" v-html="labels.get('odprodaja')"></span>
				<span v-else-if="item.last_piece_sale != 0 && item.last_piece_sale" v-html="labels.get('odprodaja_2')" class="available-last"></span>
				<span v-else v-html="labels.get('na_zalogi')"></span>
			</template>
			<template v-else-if="item?.availability_info?.status == 2">
				<span v-html="labels.get('na_zalogi_dobavitelja').replace('%MIN_DAY%', item.availability_info.min_days).replace('%MAX_DAY%', item.availability_info.max_days)"></span>
			</template>
			<template v-else-if="item?.availability_info?.status == 4">
				<span class="available-last" v-html="labels.get('na_zalogi_ena')"></span>
			</template>
			<template v-else-if="item?.availability_info?.status == 5">
				<span v-if="item?.is_available && item?.date_available" v-html="labels.get('na_voljo') + ' ' + item?.date_available"></span>
				<span v-else class="unavailable" v-html="labels.get('ni_na_zalogi_preorder')"></span>
			</template>
			<template v-else-if="item?.availability_info?.status == 7">
				<span class="unavailable" v-html="labels.get('ni_na_zalogi')"></span>
			</template>
			<template v-else-if="item?.availability_info?.status == 9">
				<span class="unavailable" v-html="labels.get('dalj_ni_na_zalogi')"></span>
			</template>
		</div>
		<div v-if="item.seller_title" class="cp-product-info-item cp-seller">
			<span><BaseCmsLabel code="seller_item_title" /> <NuxtLink :to="item.seller_url_product_page_without_domain ? item.seller_url_product_page_without_domain : item.seller_url_without_domain">{{ item.seller_title }}</NuxtLink></span>
		</div>
		<div v-if="freeShipping" class="cp-product-info-item cp-free-shipping"><BaseCmsLabel code="shipping_free_label" /></div>
	</div>
</template>

<script setup>
	const props = defineProps(['item']);
	const {freeShipping} = useProductData(props.item);
	const labels = useLabels();
</script>

<style scoped lang="less">
	.cp-product-info{font-size: 12px; line-height: 1.3;}
	.cp-product-info-item {
		display: flex; align-items: center; padding: 3px 0 4px;
		&:before{.icon-box(); width: 16px; height: 16px; display: flex; align-items: center; justify-content: center; font: 15px/1 var(--fonti); margin-right: 10px;}
	}
	.cp-free-shipping:before{.icon-truck(); font-size: 13px;}
	.cp-availability{
		&:before{.icon-check-round();}
		&.unavailable:before{.icon-x(); font-size: 11px; top: -1px;}
		:deep(strong){font-weight: normal;}
		.available-last{
			padding: 0;
			&:before{content: none;}
		}
	}
	.cp-seller{
		&:before{.icon-store(); font-size: 16px;}
		a{
			color: var(--blueDark); text-decoration: none;
			&:hover{text-decoration: underline;}
		}
	}
</style>