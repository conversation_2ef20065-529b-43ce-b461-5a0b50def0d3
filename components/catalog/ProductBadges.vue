<template>
	<div class="badges" v-if="energyAttr || priceSaved || textBadges?.length || imageBadges?.length || item.selected_price == 'uau' || item.product_condition != 'n'">
		<div v-if="energyAttr && types.includes('energy')" class="energy">
			<BaseUiImage v-if="energyAttr.image_upload_path" loading="lazy" :src="energyAttr.image_upload_path" default="/images/no-image.jpg" width="80" height="25" :title="energyAttr.title" :alt="energyAttr.title" />
			<span v-else>energyAttr.title</span>
		</div>
		
		<div v-if="priceSaved && types.includes('discount')" class="badge badge-discount red">-<BaseUtilsFormatCurrency :price="priceSaved" /></div>
		
		<div v-if="item.product_condition != 'n' && types.includes('condition')" class="badge condition-badge yellow" @click="modal.open('flyout', {header: true, title: labels.get('flyout_title_condition_' + item.product_condition), content: labels.get('flyout_content_condition_' + item.product_condition)})">
			<span v-html="labels.get('condition_' + item.product_condition)"></span>
		</div>

		<div v-if="item.selected_price == 'uau' && types.includes('uau')" class="badge badge-uau blue"><BaseCmsLabel code='uau_badge_title' /></div>

		<template v-if="imageBadges?.length && types.includes('image')">
			<div v-for="badge in imageBadges" :key="badge.code" class="image-badge">
				<BaseUiImage loading="lazy" :src="badge.badge_image_upload_path" default="/images/no-image-40.webp" width="40" height="40" :alt="badge.title" />
			</div>
		</template>
		
		<template v-if="textBadges?.length && types.includes('text')">
			<template v-for="badge in textBadges" :key="badge.code">
				<template v-if="badge.label_title">
					<div class="badge" :class="['badge-' + badge.code]">{{ badge.label_title }}</div>
				</template>
				<div v-if="badge.category == 4 && types.includes('new')" class="badge badge-new"><BaseCmsLabel code='new_badge' /></div>
			</template>
		</template>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps({
		item: {
			type: Object,
			required: true,
		},
		types: {
			type: Array,
			default: () => ['discount', 'new', 'uau', 'condition'],
		},
		energy: {
			type: Boolean,
			default: false,
		},
	});
	const modal = useModal();
	const {textBadges, imageBadges, priceSaved, energyAttr} = useProductData(props.item);
</script>

<style scoped lang="less">
	.badges{display: flex; flex-wrap: wrap; gap: 6px; align-items: center;}
	.badge{
		display: inline-flex; align-items: center; justify-content: center; min-height: 22px; padding: 2px 10px; background: var(--blueDark); border-radius: 4px; font-size: 12px; font-weight: 600; color: var(--white); white-space: nowrap; position: relative; z-index: 11;
		&.red{background: var(--errorColor);}
		&.blue{background: var(--blue);}
		&.yellow{background: #CDD700; color: var(--blueDark);}
	}
	.condition-badge{cursor: pointer;}
	.energy{
		height: 20px; font-size: 12px; position: relative; z-index: 11; width: 100%;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}
	}
	
	.cp-badges{position: absolute; bottom: 12px; left: 14px; right: 14px;}
</style>