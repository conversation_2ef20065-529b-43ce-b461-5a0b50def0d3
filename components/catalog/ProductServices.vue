<template>
	<template v-if="mode == 'badges'">
		<div class="cd-service" v-if="props.item.badges?.length" @click="modal.open('flyout', {mode: 'specialOffers', header: true, content: item.badges, headerIcon: 'fire'})">
			<div class="cd-service-title badges"><BaseCmsLabel code="special_offers" /> ({{ item.badges?.length }})</div>
		</div>
	</template>
	<template v-else>
		<div class="cd-services" v-if="item.is_available">
			<div v-if="service?.items?.length" class="cd-service"  @click="modal.open('flyout', {mode: 'productService', footer: true, header: true, headerIcon: 'services', content: item})">
				<div class="cd-service-title service" :class="{'selected': s?.length}">{{ service?.title }}</div>	
				<template v-if="selectedServices?.length">
					<div v-for="item in selectedServices" :key="item.id" class="cd-service-bid">
						<span class="title">{{ item?.title }} - </span>
						<span class="price"><BaseUtilsFormatCurrency :price="item.price" /></span>
					</div>
				</template>
			</div>

			<div v-if="insurance?.items?.length" class="cd-service" @click="modal.open('flyout', {mode: 'productInsurance', footer: true, header: true, headerIcon: 'insurance', content: item})">
				<div class="cd-service-title insurance" :class="{'selected': i?.length}">{{ insurance?.title }}</div>
				<template v-if="selectedInsurance?.length">
					<div v-for="item in selectedInsurance" :key="item.id" class="cd-service-bid">
						<span class="title">{{ item?.title }} - </span>
						<span class="price"><BaseUtilsFormatCurrency :price="item.price" /></span>
					</div>
				</template>
			</div>
		</div>
	</template>
</template>

<script setup>
	const props = defineProps({
		item: Object,
		mode: String,
	});
	const modal = useModal();
	const model = defineModel();
	
	/* 
		Get services from product data. If there are user selected services, use them. Otherwise use the first available service.
		The same goes for insurance.
		useState is used to store user selected services and insurance in product details services flyout.
	*/
	const service = computed(() => {
		if(!props.item?.services) return null;
		return props.item?.services?.find(service => service.code == 'service');
	});
	const s = useState('productSelectedServices');
	const selectedServices = computed(() => {
		if(s.value?.length) return service.value?.items.filter(item => s.value.includes(item.id));
		return [service.value?.items[0]];
	})

	const insurance = computed(() => {
		if(!props.item?.services) return null;
		return props.item?.services?.find(service => service.code == 'insurance');
	});
	const i = useState('productSelectedInsurance');
	const selectedInsurance = computed(() => {
		if(i.value) return insurance.value?.items.filter(item => item.id == i.value);
		return [insurance.value?.items[0]];
	});

	watch([selectedServices, selectedInsurance], (newValue, oldValue) => {
		const data = [];
		if(s.value?.length) data.push(...service.value?.items.filter(item => s.value.includes(item.id)));
		if(i.value) data.push(insurance.value?.items.filter(item => item.id == i.value)[0]);
		model.value = data.map(item => item.id);
	});
</script>

<style scoped lang="less">
	.cd-services{margin-top: 24px;}
	.cd-service{
		flex-grow: 1; margin-top: 16px; padding: 16px 50px 16px 15px; background: var(--gray3); border-radius: 8px; font-size: 15px; cursor: pointer; position: relative;
		&:before{.icon-arrow-right2(); font: 15px/1 var(--fonti); color: var(--black); position: absolute; right: 13px; top: 50%; transform: translateY(-50%);}

		@media (max-width: @m){
			font-size: 12px;
			&:before{right: 8px;}
		}
	}
	.cd-service-title{
		display: flex; padding: 0 0 5px 32px; font-size: 18px; font-weight: 600; position: relative;
		&:before{.icon-service(); font: 22px/1 var(--fonti); color: var(--blueDark); position: absolute; top: 1px; left: 0;}
		&.insurance:before{.icon-insurance();}
		&.badges{
			padding-bottom: 0;
			&:before{.icon-fire(); color: var(--red);}
		}
		&.selected:before{.pseudo(20px,20px); .icon-check(); font: 20px/1 var(--fonti); color: #fff; border-radius: 3px; background: var(--blueDark); top: 2px;}

		@media (max-width: @m){
			padding-left: 26px; font-size: 14px;
			&:before{font-size: 18px;}
		}
	}
	.cd-service-bid{display: block; padding-bottom: 2px;
		&:last-child{padding-bottom: 0;}
	}
</style>