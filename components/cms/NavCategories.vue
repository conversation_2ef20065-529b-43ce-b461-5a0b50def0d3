<template>
	<BaseCatalogCategoriesWidget :fetch="{limit: 0, hierarhy_by_position: true, response_fields: ['id', 'code', 'parent_code', 'total_discount', 'total_new', 'children', 'main_image_upload_path', 'main_image_2_upload_path', 'image_upload_path', 'title', 'catalogcategory_ids','url_without_domain']}" v-slot="{items: categories}" @load="onCategoriesLoad">
		<div class="c-cols">
			<div class="c-col1">
				<template v-for="item in categories" :key="item.id">
					<BaseUiLink :href="item.url_without_domain" native :prevent-default="item.children?.length ? true : false" @click="setActiveCategory(item)" :class="['c-item', {'active': activeCategory?.id == item.id}]">
						{{ item.title }}
					</BaseUiLink>
				</template>
			</div>
			<div class="c-col-wrapper" ref="scrollElementM">
				<div class="c-col2" ref="scrollElement">
					<template v-for="category in categories" :key="category.id">
						<div v-for="subcategory in category.children" :key="subcategory.code" :class="['c-subitem', {'special': !subcategory.children}, {'active': activeCategory?.id === category.id}]">
							<div class="c-subitem-header">
								<NuxtLink :to="subcategory.url_without_domain" class="title">{{ subcategory.title }}</NuxtLink>
								<NuxtLink :to="subcategory.url_without_domain" class="show-all"><BaseCmsLabel code='header_categories_show_all' /></NuxtLink>
							</div>
							<div class="c-subsubitem-wrapper" v-if="subcategory.children?.length">
								<NuxtLink v-for="subitem in subcategory.children" :key="subitem.code" :to="subitem.url_without_domain" class="c-subsubitem">
									<span v-if="subitem.main_image_2_upload_path" class="img">
										<LazyBaseUiImage v-if="subitem.main_image_2_upload_path" loading="lazy" :src="subitem.main_image_2_upload_path" default="/images/no-image.jpg" />
									</span>
									<span class="title">{{ subitem.title }}</span>
								</NuxtLink>
							</div>
						</div>
					</template>
				</div>
				<ClientOnly>
					<div class="c-col3">
						<LazyBaseCmsRotator :fetch="{code: 'menu_promo', limit: 50, response_fields: ['id','catalogcategory_ids','url_without_domain','image_thumbs', 'image_upload_path']}" v-slot="{items}" v-if="activeCategory" v-model="promoItems">
							<template v-if="items?.length && categoryHasPromo">
								<template v-for="item in items" :key="item.id">
									<template v-if="item.catalogcategory_ids.includes(activeCategory.id)">
										<NuxtLink :to="item.url_without_domain" class="c-promo">
											<LazyBaseUiImage v-if="item.image_upload_path" loading="lazy" :data="item.image_thumbs?.['width480-height2000']" default="/images/no-image.jpg" />
										</NuxtLink>
									</template>
								</template>
							</template>
						</LazyBaseCmsRotator>
						
						<LazyBaseCatalogLists v-if="!categoryHasPromo && activeCategory" :fetch="{category: activeCategory?.id}" v-slot="{items: categoryList}" watch-fetch>	
							<template v-if="categoryList?.length">
								<LazyBaseCatalogProductsWidget thumb-preset="catalogEntry" :fetch="{mode: 'widget', list_code: categoryList[0].code, sort: 'list_position', only_available: true, limit: 4}" watch-fetch v-slot="{items, loading}">
									<div v-if="items?.length" class="c-bestsellers">
										<div class="c-bestsellers-header">
											<span class="title"><BaseCmsLabel code="nav_featured_products" /></span>
										</div>
										<template v-if="loading">
											<div class="c-bestsellers-loader"><UiLoader class="inline" /></div>
										</template>
										<template v-else>
											<CatalogIndexEntryMenu v-for="item in items" :key="item.code" :item="item" />
										</template>
									</div>
								</LazyBaseCatalogProductsWidget>
							</template>
						</LazyBaseCatalogLists>
					</div>
				</ClientOnly>
			</div>
		</div>
	</BaseCatalogCategoriesWidget>
</template>

<script setup>
	const props = defineProps(['navOpen']);
	const scrollElement = ref(null);
	const scrollElementM = ref(null);
	
	const activeCategory = ref(null);
	async function setActiveCategory(item) {
		activeCategory.value = item;
	}

	// Set first category as active on load
	function onCategoriesLoad(data) {
		if(!data?.items?.length) return;
		activeCategory.value = data.items[0];
	}

	// Check if active category has promo banner
	const promoItems = ref();
	const categoryHasPromo = computed(() => {
		if(!promoItems?.value?.items?.length) return false;
		return promoItems.value.items.some(item => item.catalogcategory_ids.includes(activeCategory.value?.id))
	})
</script>


<style lang="less" scoped>
	.c-cols{display: none; background: var(--white); border-radius: 0 0 12px 12px; box-shadow: 0px 8px 18px 0px #0000001F;}
	.c-col1{
		flex-shrink: 0; width: 220px; max-height: calc(~"90vh - 132px"); padding: 24px 0; background: var(--gray3); border-radius: 0 0 0 12px; overflow: hidden; overflow-y: auto; position: relative; z-index: 11;
		&::-webkit-scrollbar{display: none;}
		&::-webkit-scrollbar-thumb{display: none;}

		@media (max-width: @t){width: 150px; max-height: calc(~"100vh - 100px");}
		@media (max-width: @m){width: auto; max-height: unset; padding: 30px 0 10px; border-radius: 0;}
	}
	.c-item{
		display: block; padding: 12px 18px; font-size: 16px; font-weight: 600; color: var(--gray5); cursor: pointer; transition: background 0.3s, color 0.3s; text-decoration: none;
		&.active{background: var(--white); color: var(--blue);}
		@media (min-width: @t){
			&:hover{background: var(--white); color: var(--blue);}
		}
		
		@media (max-width: @m){display: table-caption; margin-bottom: 20px; padding: 12px; font-size: 14px;}
	}

	.c-col-wrapper{
		display: flex;

		@media (max-width: @m){
			display: block; overflow: auto; overflow-x: hidden;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
		}
	}
	.c-col2{
		display: block; flex-shrink: 0; width: 515px; max-height: calc(~"90vh - 132px"); padding: 27px 22px; background: var(--white); border-radius: 0 0 12px 0; position: absolute; left: 0; top: 0; bottom: 0; overflow: hidden; overflow-y: auto; position: relative;
		&::-webkit-scrollbar{width: 4px; background: var(--gray2); border-radius: 6px;}
		&::-webkit-scrollbar-thumb{background: var(--blue); border-radius: 6px;}
		&::-webkit-scrollbar-track {border-top: 27px solid var(--white); border-bottom: 27px solid var(--white); border-radius: 6px;}
		&::-webkit-scrollbar-track-piece {margin-top: 27px; margin-bottom: 27px; border-radius: 6px;}

		@media (max-width: @t){width: unset; width: calc(~"100% - 550px"); max-height: calc(~"100vh - 100px");}
		@media (max-width: @m){
			flex-shrink: unset; width: 100%; max-height: unset; padding: 32px 16px 10px; border-radius: 0; position: static;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
			&::-webkit-scrollbar-track {border: none; border-radius: 0;}
			&::-webkit-scrollbar-track-piece {margin-top: 0; margin-bottom: 0; border-radius: 0;}
		}
	}
	.c-subitem{
		display: none; margin-bottom: 12px; padding-bottom: 23px; border-bottom: 1px solid #F4F4F4;
		&.active{display: block;}
		&.special{padding-bottom: 0;}
		//&:last-child{margin: 0; padding: 0; border: none;}

		@media (max-width: @m){margin-bottom: 16px; padding-bottom: 18px;}
	}
	.c-subitem-header{
		display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;
		a{color: var(--black); text-decoration: none; font-weight: 600;}
		.title{margin-right: 40px; font-size: 16px;}
		.show-all{
			display: inline-flex; align-items: center; flex-shrink: 0; padding-right: 18px; font-size: 12px; line-height: 1.6; position: relative;
			&:before{.icon-arrow-right(); font: 10px/1 var(--fonti); color: var(--black); position: absolute; right: 0;}
		}

		@media (max-width: @m){
			align-items: flex-start;
			.title{margin-right: 30px; font-size: 14px; line-height: 1.4;}
		}
	}

	.c-subsubitem-wrapper{
		display: grid; grid-template-columns: repeat(4, 1fr); gap: var(--elementGap);
	
		@media (max-width: @m){grid-template-columns: repeat(2, 1fr); grid-column-gap: 12px; grid-row-gap: 12px;}
	}
	.c-subsubitem{
		display: flex; flex-direction: column; align-items: center; text-align: center; color: var(--black); text-decoration: none; .transition(font-weight);
		.img{display: flex; align-items: center; justify-content: center; width: 64px; height: 64px; margin-bottom: 5px; flex-shrink: 0;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}
		.title{font-size: 12px;}
		@media (min-width: @t){
			&:hover{font-weight: 600;}
		}

		@media (max-width: @m){
			.img{width: 70px; height: 70px;}
		}
	}

	.c-col3{
		display: block; max-width: 418px; max-height: calc(~"90vh - 132px"); margin-right: 10px; padding: 0 12px 0 22px; background: var(--white); border-radius: 0 0 12px 0; position: absolute; left: 0; top: 0; bottom: 0; overflow: hidden; overflow-y: auto; position: relative;
		&:before{.pseudo(1px,auto); background: #F4F4F4; position: absolute; top: 27px; bottom: 27px; left: 0;}
		&::-webkit-scrollbar{width: 4px; background: var(--gray2); border-radius: 6px;}
		&::-webkit-scrollbar-thumb{background: var(--blue); border-radius: 6px;}
		&::-webkit-scrollbar-track {border-top: 27px solid var(--white); border-bottom: 27px solid var(--white); border-radius: 6px;}
		&::-webkit-scrollbar-track-piece {margin-top: 27px; margin-bottom: 27px; border-radius: 6px;}

		@media (max-width: @t){flex-shrink: 0; width: 400px; max-width: unset; max-height: calc(~"100vh - 100px");}
		@media (max-width: @m){
			width: auto; max-width: unset; max-height: unset; margin: 0; padding: 0 16px 32px; border-radius: 0;
			&:before{content: none;}
		}
	}
	.c-promo{
		display: flex; align-items: center; justify-content: center; padding: 38px 20px 38px 10px; border-radius: 12px; position: relative;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%; border-radius: 12px;}
		@media (max-width: @m){
			padding: 0; border-radius: 8px;
			:deep(img){border-radius: 12px;}
		}
	}
	.c-bestsellers{
		padding-top: 27px; min-width: 370px;
		@media (max-width: @m){padding-top: 0;}
	}
	.c-bestsellers-header{
		font-size: 16px; color: var(--black); font-weight: 600;
		@media (max-width: @m){font-size: 14px;}
	}
	.c-bestsellers-loader{text-align: center; padding: 10px;}
</style>