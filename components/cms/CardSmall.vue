<template>
	<component :is="href ? NuxtLink : 'div'" :to="props.href ? props.href : undefined" class="card">
		<div class="image">
			<slot name="header" />
		</div>
		<div class="content">
			<slot name="content" />
		</div>
	</component>
</template>

<script setup>
	import { NuxtLink } from '#components';
	const props = defineProps({
		href: String,
	})
</script>

<style scoped lang="less">
.card{
	background: #fff; border-radius: var(--borderRadius); display: flex; flex-direction: column; text-wrap: pretty; text-decoration: none; color: var(--textColor); font-weight: bold; font-size: clamp(14px, 1.5vw, 15px); line-height: 1.5;
	@media (max-width: @m){
		flex: 0 0 27%; max-width: 100px;
	}
}
.content{
	padding: 20px 13px; display: flex; align-items: center; justify-content: center; text-align: center; flex-grow: 1;
	@media (max-width: @m){
		padding: 5px 10px 10px;
	}
}
.no-content :deep(h1){padding: 0;}
.narrow-content .content{
	max-width: 50%; margin: auto;
	@media (max-width: @m){
		max-width: 100%;
	}
}
.only-image{
	.content{display: none;}
}
.image{
	position: relative; aspect-ratio: 1; display: flex; align-items: center; justify-content: center; padding: 10px;
	&:deep(img){display: block; width: auto; max-width: 100%; max-height: 100%;}
	@media (max-width: @m){height: 70px; aspect-ratio: auto;}
}
</style>