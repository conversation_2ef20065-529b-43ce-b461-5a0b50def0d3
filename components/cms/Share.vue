<template>
	<div class="share" ref="share">
		<div class="share-btn" @click="shareActive = !shareActive">
			<span v-if="props.shareLabel">Podi<PERSON>li</span>
		</div>
		<div class="share-tooltip" v-if="shareActive">
			<BaseCmsShare :networks="['link', 'email', 'facebook', 'viber', 'whatsapp', 'linkedin']" v-slot="{ items, onShare, linkCopy }">
				<div class="share-content">
					<div class="share-row">
						<template v-for="item in items" :key="item.code">
							<div v-if="['link', 'email'].includes(item.code)" class="share-item" :class="`share-item-${item.code}`" @click="onShare(item)">
								<template v-if="item.code == 'link'">
									{{ linkCopy ? 'Link copied' : 'Copy link' }}
								</template>
								<template v-else>
									Email
								</template>
							</div>
						</template>
					</div>
					<div class="share-row">
						<template v-for="item in items" :key="item.code">
							<div v-if="!['link', 'email'].includes(item.code)" class="share-item" :class="`share-item-${item.code}`" @click="onShare(item)">
								{{ item.code }}
							</div>
						</template>
					</div>
				</div>
			</BaseCmsShare>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps({
		shareLabel: {
			type: Boolean,
			default: false,
		},
	});

	const {onClickOutside} = useDom();
	const shareActive = ref(false);
	const share = ref(null);
	onClickOutside(share, () => {
		shareActive.value = false;
	});
</script>

<style scoped lang="less">
.share{position: relative;}
.share-btn{
	display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 46px; height: 46px; background: var(--white); border-radius: 100%; z-index: 1; box-shadow: 0px 0.5px 2px 0px rgba(0, 0, 0, 0.25); position: relative; cursor: pointer;
	&:before{.icon-share(); font: 22px/1 var(--fonti); color: var(--gray5);}

	@media (max-width: @m){
		width: 36px; height: 36px; margin-bottom: 8px;
		&:before{font-size: 17px;}
	}
}
.share-tooltip{
	background: #fff; color: var(--blueDark); position: absolute; top: calc(100% + 15px); width: 285px; right: -5px; box-shadow: 0px 0px 18px 0px rgba(0,0,0,.1); border-radius: var(--borderRadius); padding: 25px 25px; z-index: 10; font-size: 13px;
	&:before{
		.pseudo(10px,10px); background: #fff; position: absolute; top: -5px; right: 24px; transform: rotate(45deg);
	}
}
.share-content{display: flex; gap: 20px; flex-direction: column;}
.share-row{display: flex; align-items: center; justify-content: space-between; gap: 20px;}
.share-item{
	display: flex; align-items: center; justify-content: center; position: relative; font-size: 0; width: 30px; height: 30px; cursor: pointer;
	&:before{.icon-fb(); font: 28px/1 var(--fonti); color: var(--blueDark);}
	&.share-item-linkedin{&:before{.icon-linkedin();}}
	&.share-item-viber{&:before{.icon-viber();}}
	&.share-item-whatsapp{&:before{.icon-whatsapp();}}
	&.share-item-email, &.share-item-link{
		font-size: 13px; width: auto; display: flex; gap: 15px;
		&:before{font-size: 18px;}
	}
	&.share-item-email{
		&:before{.icon-email(); font-size: 17px;}
	}
	&.share-item-link{
		&:before{.icon-link();}
	}
}
</style>