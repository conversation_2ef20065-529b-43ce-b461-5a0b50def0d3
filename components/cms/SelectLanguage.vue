<template>
	<div @click="open = !open" class="f-language" ref="langEl">
		<span class="selected">{{ lang == 'hr' ? 'Hrvatski' : 'Slovenski'}}</span>
		<div v-show="open" class="f-language-dropdown">
			<a href="https://www.bigbang.si/">Slovenski</a>
			<a href="https://www.sancta-domenica.hr/" class="active">Hrvatski</a>
		</div>
	</div>
</template>

<script setup>
	const {lang} = useAppConfig();
	const {onClickOutside} = useDom();
	const open = ref(false);
	const langEl = ref(null);

	onClickOutside(langEl, () => {
		open.value = false;
	})
</script>

<style scoped lang="less">
	.f-language{
		display: flex; justify-content: space-between; align-items: center; cursor: pointer; width: 245px; padding: 12px 15px 12px 12px; background: var(--blueDark); border: 1px solid var(--white); border-radius: 8px; font-size: 16px; color: var(--white); position: relative;
		&:after{.icon-arrow-select(); font: 12px/1 var(--fonti); color: var(--white);}
		@media (max-width: @m){width: 100%; margin-bottom: 24px;}
	}
	.selected{
		padding: 0 0 0 28px; position: relative;
		&:before{.icon-globe(); font: 20px/1 var(--fonti); color: var(--white); position: absolute; left: 0;}
	}
	.f-language-dropdown{width: 100%; max-height: 350px; background: var(--white); border-radius: 8px; overflow: hidden; overflow-y: auto; position: absolute; left: 0; right: 0; top: calc(~"100% + 4px"); z-index: 11;}
	a{
		display: flex; text-decoration: none; align-items: center; padding: 15px; font-size: 15px; line-height: 1.35; color: var(--black);
		&:before{.pseudo(16px,16px); position: relative; background: var(--white); border: 2px solid var(--black); border-radius: 100%; margin-right: 12px; flex: 0 0 16px;}
		&.active{
			font-weight: bold;
			&:before{border-color: var(--blueDark); box-shadow: inset 0px 0px 0px 3px #fff; background: var(--blueDark);}
		}
	}
</style>