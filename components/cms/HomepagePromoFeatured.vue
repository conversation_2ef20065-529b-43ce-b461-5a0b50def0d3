<template>
	<BaseCmsRotator :fetch="{code: 'featured_promo', limit: 16, response_fields: ['id','title','code','link_target_blank','url_without_domain','image_upload_path','image_thumbs']}" v-slot="{items}">
		<div class="slider-container" v-if="items?.length">
			<div class="wrapper slider-wrapper">
				<BaseUiSwiper
					name="featured-promo"
					:options="{
						enabled: false,
						slidesPerView: 4,
						spaceBetween: 12,
						slidesPerGroup: 4,
						breakpoints: {
							980: {
								spaceBetween: 24,
								enabled: true
							}
						}
					}">
					<BaseUiSwiperSlide v-for="item in items" :key="item.id">
						<NuxtLink :target="item.link_target_blank == 1 ? '_blank' : null" :to="item.url_without_domain" class="featured-promo-slide">
							<div class="featured-promo-img">
								<BaseUiImage loading="lazy" :data="item.image_thumbs?.['width760-height399-crop1']" default="/images/no-image.jpg" />
							</div>
						</NuxtLink>
					</BaseUiSwiperSlide>
				</BaseUiSwiper>
			</div>
		</div>
	</BaseCmsRotator>
</template>

<style scoped lang="less">
	.slider-container{
		position: relative; margin-bottom: 50px;
		:deep(.swiper){overflow: initial;}
	}
	.featured-promo-img{
		display: flex; align-items: center; justify-content: center; border-radius: 12px;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%; border-radius: 12px;}

		@media (max-width: @m){
			border-radius: 8px;
			:deep(img){border-radius: 8px;}
		}
	}
</style>