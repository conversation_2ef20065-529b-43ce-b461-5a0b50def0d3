<template>
	<BaseWebshopAddToCartModal v-slot="{items, status, onClose, urls}" :auto-close="0">
		<div class="flyout" v-if="items?.length">
			<div class="flyout-body">
					<div class="close" @click="onClose">X</div>
				<div class="header">
					<BaseCmsLabel :code="status?.data?.label_name" />
				</div>
				<div class="content">
					
					<div class="item" v-for="(item, index) in items" :key="item.id">
						<div class="item-seller" v-if="item.seller_title">{{index + 1}}. Trgovac: {{item.seller_title}}</div>
						<div class="item-body">
							<div class="item-image">
								<BaseUiImage loading="lazy" :data="item?.all_images[0]?.file_thumbs?.['width240-height240']" default="/images/no-image.jpg" />
							</div>
							<div class="item-cnt">
								<div class="item-title">{{ item.title }}</div>
								<div class="item-footer">
									<div class="item-price">
										<CatalogProductPrice :item="item" />
									</div>
									<BaseWebshopRemoveProduct :item="item" v-slot="{onRemove, loading}">
										<div class="item-remove link" @click="onRemove(), onClose()">Ukloni proizvod</div>
									</BaseWebshopRemoveProduct>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="footer">
					<button class="btn-outline" @click="onClose()">Nastavi kupovinu</button>
					<NuxtLink class="btn" @click="onClose()" :to="urls.webshop_shopping_cart">Pregled košarice</NuxtLink>
				</div>
			</div>
			<div class="flyout-mask" />
		</div>
	</BaseWebshopAddToCartModal>
</template>

<script setup>

</script>

<style lang="less" scoped>
	*{
		--flyoutSideOffset: 30px;
	}
	.flyout{position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 999999; font-size: 15px;}
	.flyout-mask{position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5);}
	.flyout-body{background: #fff; width: 35%; position: fixed; top: 0; right: 0; bottom: 0; display: flex; flex-direction: column; z-index: 1;}
	.close{
		position: absolute; display: flex; align-items: center; justify-content: center; top: 30px; right: var(--flyoutSideOffset); width: 25px; height: 25px; font-size: 0; cursor: pointer;
		&:before{.icon-x(); font: 20px/1 var(--fonti);}
	}

	.header{
		font-size: 24px; padding: 25px calc(var(--flyoutSideOffset) + 50px) 20px var(--flyoutSideOffset); font-weight: bold; display: flex; gap: 10px; border-bottom: 1px solid var(--gray3);
		&.icon:before{.icon-truck(); font: 21px/1 var(--fonti); top: 7px; position: relative;}
	}
	.content{overflow: auto; flex-grow: 1;}
	.footer{
		background: #fff; padding: 20px var(--flyoutSideOffset); display: flex; gap: 10px; margin-top: auto; border-top: 1px solid var(--gray3);
	}
	button, .btn{width: 50%; height: 60px; font-size: 18px;}

	.item{padding: var(--flyoutSideOffset);}
	.item-body{display: flex; gap: 25px;}
	.item-title{padding: 0 0 15px; font-size: 15px;}
	.item-image{
		flex: 0 0 110px; display: flex; justify-content: center; align-items: flex-start;
		&:deep(img){max-width: 100%; height: auto; display: block;}
	}
	.item-footer{display: flex; justify-content: space-between; align-items: flex-end;}
	.item-seller{font-weight: bold; font-size: 18px; padding: 0 0 25px;}
	.item-cnt{flex-grow: 1;}
	.item-remove{color: var(--blueDark); font-size: 14px;}
</style>