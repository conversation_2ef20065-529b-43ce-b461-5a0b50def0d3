<template>
	<BaseWebshopCart v-slot="{cart, total}">
		<aside class="cart-box">
			<div class="cart-box-body">
				<div class="totals">
					<div class="subtotal">
						<BaseCmsLabel code="total_to_pay_cart" />: <span><BaseUtilsFormatCurrency :wrap="true" :price="total.total_items_total" /></span>
						<div class="tax">Uklj. <template v-if="currentLang === 'hr'">PDV</template><template v-else>DDV</template></div>
					</div>
					<!--
					Faza 2
					<div class="total" v-if="usedCoupon?.total"><BaseCmsLabel code="coupon_discount" />: <span><BaseUtilsFormatCurrency :price="usedCoupon.total" /></span></div>
					<div class="total"><BaseCmsLabel code="value_card" />: <span>- 100,00</span></div>
					-->
					<!--
					<div class="other-totals">
						<div class="total red">
							<BaseCmsLabel code="total_discount" />: <span>50,00 €</span>
						</div>
					</div>
					-->
					<div class="other-totals" v-if="giftCard || total.discount">
						<div v-if="giftCard" class="total"><BaseCmsLabel code="value_card" />: <span>- <BaseUtilsFormatCurrency :price="giftCard" /></span></div>
						<div class="total red" v-if="total.discount"><BaseCmsLabel code="total_discount" />: <span><BaseUtilsFormatCurrency :price="total.discount" /></span></div>
					</div>
				</div>
				
				<div class="note"><BaseCmsLabel code="total_without_shipping" /></div>
				
				<WebshopCouponForm />
				
				<!--<div class="note">Vsi morebitni popusti za Klub Big Bang (številka kartice %LOYALTY_CODE%) so že upoštevani.</div>-->
				<div class="note note-loyalty" v-if="cartData?.customer?.loyalty && totalLoyalty" v-html="totalLoyalty"></div>

				<div class="cart-box-btn" ref="addToCartButton">
					<BaseWebshopCartErrors v-slot="{errors, errorsItems, warnings}">
						<NuxtLink :to="getAppUrl('webshop_customer')" class="btn" :class="{'btn-disabled': errors || errorsItems || warnings}">
							<span><BaseCmsLabel code="checkout" /></span>
						</NuxtLink>
						<!--<div class="cart-error"><BaseCmsLabel code="cart_warning_next_step_note" /></div>-->
						<div v-if="errors || errorsItems || warnings" class="cart-error"><BaseCmsLabel code="cart_warning_next_step_note" /></div>
					</BaseWebshopCartErrors>
				</div>
			</div>

			<div class="benefits" ref="el2">
				<div class="benefit">
					<span><BaseCmsLabel code="safe_payment" /></span>
				</div>
				<div class="benefit return">
					<span @click="modal.open('flyout', {title: labels.get('return_policy_flyout_title'), header: true, content: labels.get('return_policy_info')})"><BaseCmsLabel code="return_policy_value" /></span>
				</div>
			</div>

			<BaseCmsLabel class="cards" code="cards" tag="div" />
		</aside>
		
		<teleport to="body">
			<div class="add-to-cart-fixed" v-show="!isVisible">
				<BaseWebshopCartErrors v-slot="{errors, errorsItems, warnings}">
					<NuxtLink :to="getAppUrl('webshop_customer')" class="btn" :class="{'btn-disabled': errors || errorsItems || warnings}">
						<span><BaseCmsLabel code="checkout" /></span>
					</NuxtLink>
					<div class="add-to-cart-fixed-note" v-if="labels.get('free_shipping_order')"><BaseCmsLabel code="free_shipping_order" /></div>
				</BaseWebshopCartErrors>
			</div>
		</teleport>
	</BaseWebshopCart>
</template>

<script setup>
	const webshop = useWebshop();
	const cartData = computed(() => webshop.getCartData());
	const labels = useLabels();
	const modal = useModal();
	const {get: currentLang} = useLang();
	const {onElementVisibility} = useDom();
	const {getAppUrl} = useApiRoutes();

	// Fixed checkout button on mobile screens
	const addToCartButton = ref(null);
	const {isVisible} = onElementVisibility({
		target: addToCartButton
	});

	// gift card
	const giftCard = computed(() => {
		const giftCardPayment = cartData.value?.cart?.payments?.selected?.find(el => el.widget == 'giftcard');
		if(!giftCardPayment) return null;

		const giftCardData = giftCardPayment?.giftcard_data?.find(el => el.extra_info != null);
		if(giftCardData) {
			return giftCardData.extra_info.amount_to_be_used;
		}
	});

	const usedCoupon = computed(() => {
		return cartData.value?.total?.extraitems?.find(el => el.type == 'coupon') ? cartData.value.total.extraitems.find(el => el.type == 'coupon') : null;
	});

	//loyalty code
	const totalLoyalty = computed(() => {
		let loyalty_code = cartData.value && cartData.value.customer?.loyalty ? cartData.value.customer?.loyalty : null;
		let desc = labels.get('total_with_loyalty') ? labels.get('total_with_loyalty') : null;
		if (desc && loyalty_code) {
			desc = desc.replace('%LOYALTY_CODE%', loyalty_code);
		}
		return desc;
	});
</script>

<style lang="less" scoped>
	.cart-box{
		background: #fff; border-radius: var(--borderRadius); padding: 15px;
		@media (max-width: @m){border-radius: 0;}
	}
	.totals{padding-bottom: 15px;}
	.subtotal{font-size: clamp(20px, 1.5vw, 24px); font-weight: bold; display: flex; justify-content: space-between; flex-wrap: wrap;}
	.other-totals{display: flex; flex-direction: column; font-size: 14px; font-weight: bold; padding: 15px 0 0;}
	.total{display: flex; justify-content: space-between;}
	.tax{font-size: 10px; color: var(--gray5); font-weight: normal; width: 100%; text-align: right;}
	.note{font-size: 12px; padding: 0 0 25px; color: var(--gray5); border-top: 1px solid var(--gray3); padding: 8px 0 0;}
	.btn{width: 100%;}
	.cart-box-return{display: flex;}
	.cart-box-btn{padding: 10px 0 30px; display: flex; flex-direction: column; gap: 15px;}
	.cart-error{font-size: 14px; color: var(--red); max-width: 300px; margin: auto; font-weight: bold; text-align: center; text-wrap: balance;}
	.benefits{display: flex; gap: clamp(20px, 2vw, 35px); justify-content: center; font-size: clamp(11px, 2vw, 12px); padding-bottom: clamp(20px, 2vw, 15px);}
	.benefit{
		span{
			position: relative; display: flex; align-items: center; gap: 7px;
			&:before{.icon-card(); font: 14px/1.5 var(--fonti); }
		}
		&.return{
			cursor: pointer;
			span:before{
				.icon-repeat(); font-size: 15px;
			}
		}
	}
	.cards{
		display: flex; justify-content: center; align-items: center; gap: 8px; flex-wrap: wrap;
		:deep(img){display: block;
			@media (max-width: @m){max-width: 30px; max-height: 30px;}
		}
	}
	.add-to-cart-fixed, .add-to-cart-fixed-note{display: none;}
	@media (max-width: @m){
		.add-to-cart-fixed{display: block; position: fixed; bottom: 0; left: 0; right: 0; z-index: 100; background: #fff; padding: 15px; border-top: 1px solid var(--gray3);}
		.add-to-cart-fixed-note{display: block; font-size: 12px; text-align: center; padding: 8px 0 0;}
	}
</style>
