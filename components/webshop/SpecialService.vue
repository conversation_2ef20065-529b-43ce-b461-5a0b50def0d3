<template>
	<!--
	<div class="extra extra-special-service loading">
		<input  type="checkbox" checked />
		<label><BaseCmsLabel code="selected_assembly" />: <strong><PERSON><PERSON>e</strong></label>
		<UiLoader mode="dots" class="dark" />
	</div>
	-->
	<div class="extra extra-special-service" :class="{'loading': loading}" v-if="specialService" @click="updateService()">
		<input type="checkbox" checked v-if="isSelected" />
		<label><BaseCmsLabel code="selected_assembly" />: <strong v-if="specialService.options">{{specialService.options.selected_value}}</strong></label>
		<UiLoader mode="dots" class="dark" v-if="loading" />
	</div>
</template>

<script setup>
	const webshop = useWebshop();
	const props = defineProps(['data']);

	const serviceId = ref();
	const isSelected = ref(0);
	const loading = ref(false);

	const specialService = computed(() => {
		if(props.data.services?.special[0]) {
			serviceId.value = props.data.services.special[0].id;
			return props.data.services.special[0];
		}
	})

	function checkSelected() {
		if(props.data.services?.special[0]) {
			isSelected.value = (props.data.services.special[0].options.selected_value == 'z montažo') ? serviceId : 0;
		}
	}

	checkSelected();
	watch(() => props.data, () => {
		checkSelected();
	});

	const updateService = async () => {
		let data = [];
		loading.value = true;

		// get selected insurances
		if(props.data.insurances && props.data.insurances.selected){
			props.data.insurances.selected.forEach(el => {
				data.push(el.id);
			})
		}

		// get selected services
		if(props.data.services && props.data.services.selected){
			props.data.services.selected.forEach(el => {
				if(el.id != serviceId.value) {
					data.push(el.id);
				}
			})
		}

		// update product (delete current product and add a new one)
		const quantity = props.data.quantity;
		const selectedOffer = props.data.services.special[0].selected_offer_id;
		const replacedOffer = props.data.services.special[0].replaced_offer_id;

		await webshop.removeProduct([{shopping_cart_code: selectedOffer}], true);
		await webshop.addProduct([{
			shopping_cart_code: replacedOffer,
			quantity: quantity,
			services: data
		}]);

		data = [];
		loading.value = false;
	}
</script>

<style lang="less" scoped>
	.extra{
		border: 1px solid var(--gray2); width: 100%; border-radius: 8px; display: flex; padding: 14px 15px; cursor: pointer; font-size: 15px; position: relative; .transition(border-color); align-items: center;
		&:after{.icon-arrow-right2(); font: 15px/1 var(--fonti); position: absolute; right: 15px; top: 50%; transform: translateY(-50%);}
		&:hover{border-color: var(--blueDark);}
		&:deep(.dots-loader){margin: 0 30px 0 auto;}
	}
	.loading{pointer-events: none;}
</style>