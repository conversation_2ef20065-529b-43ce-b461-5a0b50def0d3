<template>
	<FlyoutLayout>
		<template #header>
			<BaseCmsLabel code="comments_show_form" :strip-html="true" />
		</template>
		<template #content>
			<div class="form">
				<BaseFeedbackCommentsForm class="form-comment" v-slot="{fields, onReset, status, loading}" :fields-config="fieldConfig">
					<template v-if="!status?.success">
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage}">
							<BaseFormInput v-if="item.type == 'hidden'" />
							<p v-else class="field" :class="['field-' + item.name, {'hidden': item.type == 'hidden'}]">
								<label :for="item.name">
									<BaseCmsLabel :code="item.name == 'message' ?  'form_comments_message' : item.name" /> 
									<template v-if="item.validation?.find(rule => rule.type == 'not_empty')"> *</template>
								</label>
								<BaseFormInput />
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>
						<button class="btn btn-send-comment" type="submit" :disabled="loading">
							<UiLoader mode="dots" v-if="loading" />
							<template v-else><BaseCmsLabel code="send_comment" /></template>
						</button>
						<div class="note"><BaseCmsLabel code="comment_form_note" /></div>
					</template>

					<div class="success" v-if="status?.success">
						<BaseCmsLabel code="comment_success" tag="div" />
					</div>
				</BaseFeedbackCommentsForm>
			</div>
		</template>
	</FlyoutLayout>
</template>

<script setup>
	const fieldConfig = {
		order: ['rate', 'display_name', 'email', 'message'],
	};
</script>

<style scoped lang="less">
	.form{padding: var(--flyoutSideOffset);}
	.field{padding-bottom: 25px;}
	label{padding: 0 0 10px;}
	.error{padding-top: 10px;}
	.field-rate{
		label{display: none;}
		.error{text-align: center;}
	}
	:deep(.form-field-rate-items){
		display: flex; gap: 30px; justify-content: center;
		label{font-size: 0; padding: 0; display: block; width: 26px; height: 26px; display: flex; align-items: center; justify-content: center;}
		input[type=radio]+label:before{
			.icon-star2(); font: 22px/1 var(--fonti); color: var(--gray5); border: 0; width: 100%; height: 100%; color: var(--textColor); background: none; position: relative; display: flex; align-items: center; justify-content: center;
		}
		.form-rate-item{
			&.active, &.active-hover{
				input[type=radio]+label:before{.icon-star3();}
			}
		}
	}
	.note{padding: 20px 30px 0 0; text-align: center; text-wrap: balance;}
	.btn{width: 100%;}
	.success{font-size: 16px; color: var(--gray7);}
</style>