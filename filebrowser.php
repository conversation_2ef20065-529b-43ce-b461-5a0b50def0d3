<?php

defined('SYSPATH') or die('No direct script access.');

return [
    'generate_thumb' => [
        'big' => ['width' => 750, 'height' => NULL, 'crop' => FALSE],
        'big_crop' => ['width' => 750, 'height' => 320, 'crop' => TRUE],
        'medium' => ['width' => 350, 'height' => NULL, 'crop' => FALSE],
        'medium_crop' => ['width' => 350, 'height' => 230, 'crop' => TRUE],
        'small_crop' => ['width' => 220, 'height' => 140, 'crop' => TRUE],
    ],

	'generate_thumb_upload' => [
		'page-image' => [
			// SD
			['width' => 1440, 'height' => 530, 'crop' => TRUE],
		],
		'rotatorelement-image' => [
			['width' => 980, 'height' => 980, 'crop' => FALSE],
			['width' => 1480, 'height' => 540, 'crop' => TRUE],
			['width' => 1480, 'height' => 160, 'crop' => TRUE],
			['width' => 730, 'height' => 220, 'crop' => TRUE],
			['width' => 730, 'height' => 540, 'crop' => FALSE],
			['width' => 730, 'height' => 260, 'crop' => FALSE],
			['width' => 724, 'height' => 380, 'crop' => TRUE],
			['width' => 75, 'height' => 75, 'crop' => TRUE],
			['width' => 355, 'height' => 180, 'crop' => TRUE],
			['width' => 230, 'height' => 100, 'crop' => FALSE],
			['width' => 480, 'height' => 2000, 'crop' => FALSE],
			['width' => 730, 'height' => 2000, 'crop' => FALSE],

			// SD
			['width' => 1920, 'height' => 500, 'crop' => TRUE],
			['width' => 1920, 'height' => 200, 'crop' => TRUE],
			['width' => 960, 'height' => 670, 'crop' => FALSE],
			['width' => 952, 'height' => 464, 'crop' => TRUE],
			['width' => 760, 'height' => 399, 'crop' => TRUE],
			['width' => 480, 'height' => 2000, 'crop' => FALSE],
			['width' => 464, 'height' => 464, 'crop' => TRUE],
			['width' => 269, 'height' => 226, 'crop' => TRUE],
			['width' => 269, 'height' => 477, 'crop' => TRUE],
			['width' => 200, 'height' => 200, 'crop' => FALSE],
		],
		'rotatorelement-image_2' => [
			['width' => 760, 'height' => 390, 'crop' => TRUE],
			['width' => 760, 'height' => 260, 'crop' => TRUE],
			['width' => 724, 'height' => 380, 'crop' => TRUE],
			['width' => 140, 'height' => 70, 'crop' => FALSE],
			['width' => 390, 'height' => 270, 'crop' => TRUE],
		],
		'rotatorelement-image_4' => [
			['width' => 760, 'height' => 397, 'crop' => TRUE],			
		],
		'menuitem-image' => [
			['width' => 155, 'height' => 60, 'crop' => FALSE],
		],		
		'catalogcategory-image' => [
			['width' => 75, 'height' => 75, 'crop' => FALSE],
		],
		'catalogcategory-image_2' => [
			['width' => 140, 'height' => 140, 'crop' => FALSE],
			['width' => 75, 'height' => 75, 'crop' => FALSE],
		],
		'catalogcategoryrotatorelement-image' => [
			// SD
			['width' => 275, 'height' => 490, 'crop' => TRUE],
		],
		'catalogmanufacturer-image' => [
			['width' => 170, 'height' => 80, 'crop' => FALSE],
		],
		'catalogproduct-energy_image' => [
			['width' => 300, 'height' => 585, 'crop' => FALSE],
		],
		'catalogproductfile-file' => [
			['width' => 1500, 'height' => 1500, 'crop' => FALSE],
			['width' => 640, 'height' => 640, 'crop' => FALSE],
			['width' => 110, 'height' => 110, 'crop' => FALSE],
			['width' => 240, 'height' => 240, 'crop' => FALSE],
			['width' => 70, 'height' => 70, 'crop' => FALSE],

			// SD
			['width' => 640, 'height' => 640, 'crop' => FALSE],
			['width' => 400, 'height' => 400, 'crop' => FALSE],
			['width' => 240, 'height' => 240, 'crop' => FALSE],
			['width' => 110, 'height' => 110, 'crop' => FALSE],
		],
		'cataloglist-image' => [
			['width' => 888, 'height' => 324, 'crop' => FALSE],
			['width' => 355, 'height' => 595, 'crop' => TRUE],
		],
		'cataloglist-image_2' => [
			['width' => 760, 'height' => 760, 'crop' => FALSE],
		],
		'cataloglist-image_3' => [
			['width' => 240, 'height' => 300, 'crop' => TRUE],

			// SD
			['width' => 269, 'height' => 477, 'crop' => TRUE],
		],
		'cataloglist-image_4' => [
			['width' => 760, 'height' => 760, 'crop' => FALSE],

			// SD
			['width' => 269, 'height' => 477, 'crop' => TRUE],
		],
		'cataloglist-image_5' => [
			['width' => 240, 'height' => 300, 'crop' => TRUE],

			// SD
			['width' => 269, 'height' => 477, 'crop' => TRUE],
		],
		'cataloglist-image_6' => [
			['width' => 760, 'height' => 760, 'crop' => FALSE],

			// SD
			['width' => 269, 'height' => 477, 'crop' => TRUE],
		],
		'catalogseller-image' => [
			['width' => 146, 'height' => 146, 'crop' => FALSE],

			// SD
			['width' => 200, 'height' => 200, 'crop' => FALSE],
		],
		'publishcategory-image' => [
			['width' => 480, 'height' => 220, 'crop' => TRUE],

			// SD
			['width' => 1440, 'height' => 530, 'crop' => TRUE],
			['width' => 500, 'height' => 260, 'crop' => TRUE],
			['width' => 200, 'height' => 200, 'crop' => false],
		],
		'publishcategory-image_3' => [
			['width' => 980, 'height' => 980, 'crop' => FALSE],
		],
		'publishfile-file' => [
			['width' => 1480, 'height' => 540, 'crop' => TRUE],
			['width' => 920, 'height' => 920, 'crop' => FALSE],
			['width' => 760, 'height' => 398, 'crop' => TRUE],
			['width' => 730, 'height' => 382, 'crop' => TRUE],
			['width' => 800, 'height' => 385, 'crop' => TRUE],
			['width' => 480, 'height' => 220, 'crop' => TRUE],
			['width' => 475, 'height' => 230, 'crop' => TRUE],
			['width' => 170, 'height' => 130, 'crop' => TRUE],
			['width' => 160, 'height' => 151, 'crop' => TRUE],
			
			// SD
			['width' => 1480, 'height' => 540, 'crop' => TRUE],
			['width' => 1440, 'height' => 530, 'crop' => TRUE],
			['width' => 800, 'height' => 600, 'crop' => TRUE],
			['width' => 480, 'height' => 220, 'crop' => TRUE],
		],
		'staticcontentpage-image' => [
			['width' => 240, 'height' => 300, 'crop' => TRUE],
		],
		'staticcontentpage-image2' => [
			['width' => 240, 'height' => 300, 'crop' => TRUE],
		],
		'staticcontentpageitem-image' => [
			['width' => 1920, 'height' => 800, 'crop' => FALSE],
			['width' => 1480, 'height' => 1480, 'crop' => FALSE],
			['width' => 760, 'height' => 760, 'crop' => FALSE],
			['width' => 740, 'height' => 400, 'crop' => TRUE],
			['width' => 480, 'height' => 800, 'crop' => FALSE],
			['width' => 480, 'height' => 480, 'crop' => FALSE],
			['width' => 475, 'height' => 230, 'crop' => TRUE],
			['width' => 240, 'height' => 240, 'crop' => FALSE],
		],
		'staticcontentpageitem-image2' => [
			['width' => 150, 'height' => 60, 'crop' => FALSE],
			['width' => 760, 'height' => 760, 'crop' => FALSE],
		],
        'faqcategory-image' => [
            ['width' => 480, 'height' => 220, 'crop' => TRUE],
            
			// SD
			['width' => 600, 'height' => 340, 'crop' => TRUE],
        ],
		'locationpointfile-file' => [
			['width' => 640, 'height' => 320, 'crop' => TRUE],
			['width' => 300, 'height' => 140, 'crop' => TRUE],
		],
	],	
];
