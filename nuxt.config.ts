import {config} from './hapi.config';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
	extends: ['../nuxt-base'],
	css: ['~/assets/style.css'],
	vite: {
		css: {
			preprocessorOptions: {
				less: {
					additionalData: `@import "~/assets/_vars.less"; @import "~/assets/_mixins.less";`,
				},
			},
		},
		define: {
			__GENERATE_THUMBS__: JSON.stringify(true),
			__SELLERS__: JSON.stringify(true),
			__STRUCTURED_DATA__: JSON.stringify(true),
			__WEBSHOP_VIEW_ORDER__: JSON.stringify(false),
			__LOCATIONS__: JSON.stringify(true),
			__CATALOG_MAIN_LANDING__: JSON.stringify(true),
			__CATALOG_CATEGORY_LANDING__: JSON.stringify(true),
		},
	},
	vue: {
		compilerOptions: {
			isCustomElement: tag => ['bb-3dviewer'].includes(tag),
		},
	},	
	dir: {
		'public': 'media',
	},
	compressPublicAssets: {
		gzip: true,
		brotli: true,
	},
	$production: {
		sourcemap: {
			server: false,
			client: false,
		},
		/*
		routeRules: {
			'*': {
				swr: config[config.default].routeCacheExpire,
			},
		},
		*/
	},
	nitro: {
		minify: true,
		storage: {
			db: config[config.default].redis,
		},
		devStorage: {
			db: {
				driver: 'fs',
				base: '.app_cache_data',
			},
		},
	},
	devtools: {
		enabled: false,
	},
	experimental: {
		asyncContext: true,
		clientFallback: true,
		emitRouteChunkError: 'automatic',
	},
	compatibilityDate: '2024-09-17',
});
