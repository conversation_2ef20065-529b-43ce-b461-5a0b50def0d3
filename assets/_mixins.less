.transition (@element: color, @speed: .3s) {
	transition: @arguments;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent;
}
.grayscale (@bw: 100%) {
	filter: grayscale(@bw);
	-webkit-filter: grayscale(@bw);
	-moz-filter: grayscale(@bw);
}
.gradient(@startColor: #eee, @endColor: white) {
	background: @startColor;
	background-image: linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -webkit-linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -moz-linear-gradient(@startColor 0%, @endColor 100%);
	background-image: -ms-linear-gradient(180deg, @startColor 0%, @endColor 100%);
}
.horizontal-gradient (@startColor: #eee, @endColor: white) {
 	background-color: @startColor;
	background-image: -webkit-linear-gradient(90deg, @startColor, @endColor);
	background-image: -ms-linear-gradient(90deg, @startColor, @endColor);
	background-image: linear-gradient(90deg, @startColor, @endColor);
}
.pseudo(@width, @height) {
	content:"";
	position: absolute;
	display: block;
	width:@width;
	height:@height;
}
.animation (@name, @duration: 300ms, @delay: 0, @ease: ease) {
	-webkit-animation: @name @duration @delay @ease;
	-moz-animation:    @name @duration @delay @ease;
	-ms-animation:     @name @duration @delay @ease;
}
.transform(@string){
	transform: @string;
	-webkit-transform: @string;
	-ms-transform: 		 @string;
}
.scale (@factor) {
	transform: scale(@factor);
	-webkit-transform: scale(@factor);
	-ms-transform: 		 scale(@factor);
}
.scaleX(@factor) {
	transform: scaleX(@factor);
	-webkit-transform: scaleX(@factor);
	-ms-transform: 		 scaleX(@factor);	
}
.scaleY (@factor) {
	transform: scaleY(@factor);
	-webkit-transform: scaleY(@factor);
	-ms-transform: scaleY(@factor);
}
.rotate (@deg) {
	transform: rotate(@deg);
	-webkit-transform: rotate(@deg);
	-ms-transform: 		 rotate(@deg);
}
.translate (@x, @y:0) {
	transform: translate(@x, @y);
	-webkit-transform: translate(@x, @y);
	-ms-transform: translate(@x, @y);
}
.translate3d (@x, @y: 0, @z: 0) {
	transform: translate3d(@x, @y, @z);
	-webkit-transform: translate3d(@x, @y, @z);
	-ms-transform: translate3d(@x, @y, @z);
}
.clear() { 
	/**zoom: 1;*/ clear:both;
	&:before, &:after {content:""; display: table; }
	&:after { clear: both; }
}
.placeholder(@color,@focusColor) {
	&::-webkit-input-placeholder { color: @color; }
	&:-ms-input-placeholder { color: @color; }
	&::-moz-placeholder { color: @color; }
	&:focus {
		&::-webkit-input-placeholder { color: @focusColor; }
		&:-ms-input-placeholder { color: @focusColor; }
		&::-moz-placeholder { color: @focusColor; }
	}
}

//Icons
.icon-star3(){
  content: "\e93f";
}
.icon-star2(){
  content: "\e93e";
}
.icon-star(){
  content: "\e93d";
}
.icon-link2(){
  content: "\e93c";
}
.icon-close(){
  content: "\e93b";
}
.icon-clock2(){
  content: "\e93a";
}
.icon-layers(){
  content: "\e939";
}
.icon-date(){
  content: "\e938";
}
.icon-arrows-horizontal(){
  content: "\e937";
}
.icon-whatsapp(){
  content: "\e935";
}
.icon-viber(){
  content: "\e936";
}
.icon-bin(){
  content: "\e933";
}
.icon-link(){
  content: "\e934";
}
.icon-phone(){
  content: "\e932";
}
.icon-email(){
  content: "\e931";
}
.icon-arrow-right2(){
  content: "\e930";
}
.icon-arrow-select(){
  content: "\e92f";
}
.icon-insurance(){
  content: "\e92e";
}
.icon-clock(){
  content: "\e92d";
}
.icon-like(){
  content: "\e92c";
}
.icon-wishlist-full(){
  content: "\e92b";
}
.icon-check-round(){
  content: "\e91c";
}
.icon-filter(){
  content: "\e920";
}
.icon-play(){
  content: "\e925";
}
.icon-plus(){
  content: "\e92a";
}
.icon-energy(){
  content: "\e91d";
}
.icon-pin(){
  content: "\e91e";
}
.icon-shipping(){
  content: "\e91f";
}
.icon-service(){
  content: "\e921";
}
.icon-box(){
  content: "\e922";
}
.icon-eye(){
  content: "\e923";
}
.icon-list(){
  content: "\e924";
}
.icon-share(){
  content: "\e926";
}
.icon-zoomIn(){
  content: "\e927";
}
.icon-fire(){
  content: "\e928";
}
.icon-d(){
  content: "\e929";
}
.icon-refresh(){
  content: "\e918";
}
.icon-grid(){
  content: "\e919";
}
.icon-arrows(){
  content: "\e91a";
}
.icon-arrow-fill(){
  content: "\e91b";
}
.icon-check(){
  content: "\e917";
}
.icon-age(){
  content: "\e916";
}
.icon-frame(){
  content: "\e915";
}
.icon-arrow-left(){
  content: "\e914";
}
.icon-truck(){
  content: "\e913";
}
.icon-wishlist(){
  content: "\e912";
}
.icon-info(){
  content: "\e911";
}
.icon-headphones(){
  content: "\e910";
}
.icon-cart(){
  content: "\e90f";
}
.icon-store(){
  content: "\e90e";
}
.icon-arrow-right(){
  content: "\e90d";
}
.icon-x(){
  content: "\e90c";
}
.icon-user(){
  content: "\e90b";
}
.icon-repeat(){
  content: "\e90a";
}
.icon-arrow-down(){
  content: "\e909";
}
.icon-search(){
  content: "\e908";
}
.icon-card(){
  content: "\e907";
}
.icon-linkedin(){
  content: "\e906";
}
.icon-yt(){
  content: "\e905";
}
.icon-tik-tok(){
  content: "\e904";
}
.icon-ing(){
  content: "\e903";
}
.icon-fb(){
  content: "\e902";
}
.icon-globe(){
  content: "\e901";
}
.icon-tag(){
  content: "\e900";
}