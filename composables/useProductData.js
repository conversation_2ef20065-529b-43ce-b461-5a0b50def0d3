export function useProductData(item) {
	const auth = useAuth();

	// Dva slikovna badgea koji se prikazuju na iznad slike
	const displayedBadges = computed(() => {
		return item?.badges_special_1 ? item?.badges_special_1?.slice(0, 2) : [];
	});

	const textBadges = computed(() => {
		const badges = item?.badges?.filter(attr => !attr.badge_image) || [];
		return badges.length > 3 ? badges.slice(0, 3) : badges;
	});

	// Filtered badges with image
	const imageBadges = computed(() => {
		const badges = item?.badges?.filter(attr => attr.badge_image) || [];
		return badges.length > 3 ? badges.slice(0, 3) : badges;
	});

	//badge discount
	const priceSaved = computed(() => {
		if (item.selected_price == 'recommended' && (item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)) {
			return item.price_custom - item.loyalty_price_custom;
		} else {
			return item.basic_price_custom - item.price_custom;
		}
	});

	//energy image
	const energyAttr = computed(() => {
		const targetAttributeCodes = ['ucinek-pranja-in-su-100218739', 'razred-energijske-u-100215480', 'razred-energijske-u-100176542', 'razred-energij-ucinkov-35', 'bf001083', 'bf000029'];
		return item?.attributes_special?.find(attr => targetAttributeCodes.includes(attr.attribute_code)) || null;
	});

	//conf items label
	const priceFrom = computed(() => {
		if (['advanced', 'configurable'].includes(item.type) && item.basic_price_custom > item.price_custom) {
			return true;
		}
		return false;
	});

	//installments
	const installmentPrice = computed(() => {
		if (item?.installments_calculation?.regular) {
			const values = Object.values(item.installments_calculation.regular);
			return Math.min(...values);
		}
	});
	const maxInstallments = computed(() => {
		if (item?.installments_calculation?.regular) {
			const values = Object.keys(item.installments_calculation.regular);
			return Math.max(...values);
		}
	});	
		
	const isLoyalty = computed(() => {
		const user = auth.getUser();
		return user?.loyalty_code ? true : false;
	});

	// If type of price is set to "Recommended"
	const isRecommendedPrice = computed(() => {
		if (item.selected_price == 'recommended' && (item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom) && ((!item.loyalty_price_custom || isLoyalty.value == false) || (isLoyalty.value == true && item.loyalty_price_custom && item.price_custom < item.loyalty_price_custom))) return true;
		return false;
	});

	const isDiscountPrice = computed(() => {
		if ((item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom) && ((!item.loyalty_price_custom || isLoyalty.value == false) || (isLoyalty.value == true && item.loyalty_price_custom && item.price_custom < item.loyalty_price_custom))) return true;
		return false;
	});

	// If type of price is set to "Promotion 2"
	const isPromoPrice = computed(() => {
		if (['promotion', 'promotion2'].includes(item.selected_price) && ((!item.loyalty_price_custom || isLoyalty.value == false) || (isLoyalty.value == true && item.loyalty_price_custom && item.price_custom < item.loyalty_price_custom))) return true;
		return false;
	});

	const isLoyaltyPrice = computed(() => {
		if (isLoyalty.value && item.loyalty_price_custom && item.loyalty_price_custom < item.basic_price_custom) return true;
		return false;
	});
	
	const freeShipping = computed(() => {
		if (item?.shipping_options?.find(option => option.shipping_price <= 0)) return true;
		return false;
	});

	return {
		displayedBadges,
		imageBadges,
		textBadges,
		priceSaved,
		energyAttr,
		priceFrom,
		installmentPrice,
		maxInstallments,
		isLoyalty,
		isRecommendedPrice,
		isDiscountPrice,
		isPromoPrice,
		isLoyaltyPrice,
		freeShipping
	};
}